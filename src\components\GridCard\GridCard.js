import { Link } from "@material-ui/core";
import RctCollapsibleCard from "Components/RctCollapsibleCard/RctCollapsibleCard";
import React from "react";
import { GridCardFooter } from "./GridCardFooter";

export const GridCard = ({
  id,
  title,
  thumbnail,
  full = false,
  leading,
  contents,
  footer,
  isRow = false
}) => {
  return (
    <RctCollapsibleCard
      colClasses={full ? "w-xs-full" : "col-sm-12 col-md-6 col-lg-4 w-xs-full"}
      contentClasses={isRow ? "row" : ""}
      heading={
        <div className="d-flex justify-content-between align-items-center">
          {(footer && footer.onEdit) ? <Link onClick={footer.onEdit}>{title}</Link> : title}
          {leading}
        </div>
      }
      thumbnail={thumbnail && (
        <div className="thumbnail">
          <img className="img-fluid" src={thumbnail} />
        </div>
      )}
      foot={footer && <GridCardFooter {...footer} />}
    >
      {contents && contents.map((children, id) => React.cloneElement(children, { key: `GridCard-Item-${id}` }))}
    </RctCollapsibleCard>
  );
}
