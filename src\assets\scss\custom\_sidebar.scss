/*============ Sidebar Layout ===========*/
.responsive-sidebar-toggler {
   position: fixed;
   z-index: 10;
   left: 1.25rem;
   top: 1rem;
   button:hover,
   button {
      background: $qiplus-color;
      svg {
         color: $white;
      }
   }
}

.rct-page-wrapper {
   [direction="right"] {
      border-right: none;
      z-index: 999 !important;
   }
}
.rct-sidebar {
   background-repeat: no-repeat;
   background-size: cover;
   width: $sidebar-width;
   background-position: top center;
   overflow: hidden;
   transition: all 200ms ease 0s;
   height: 100%;
   background-color: $qiplus-color;
   background-blend-mode: saturation;
   .sidebar-collapse-btn {
      position: absolute;
      right: 0;
      top: 0;
      width: 28px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      transform: scale(0.8);
      /* border: 2px solid rgba(255,255,255,0.7); */
      // border-radius: 50%;
      i {
         color: #f16c2b;
      }
   }
   .rct-sidebar-content {
      position: relative;
      z-index: 0;
      &:before {
         position: absolute;
         top: 0;
         left: 0;
         right: 0;
         bottom: 0;
         content: "";
         z-index: -1;
         // opacity: 0.9;
         opacity: 1;
      }
      &.sidebar-overlay-dark:before {
         background: $sidebar-dark-gradient;
      }
      &.sidebar-overlay-light:before {
         background-color: $overlay-light;
      }
   }
   &.background-none .rct-sidebar-content:before {
      opacity: 1 !important;
   }
   .site-logo {
      padding: 0.5rem 1.5rem; //15px 30px;
      align-items: center;
      display: flex;
      background-color: #383e54;
      .logo-normal {
         width: 100%;
         display: flex;
      }
   }
   .rct-sidebar-wrap {
      height: calc(100vh - #{$sidebar-logo-height});
      .rct-scroll {
         min-height: calc(100vh - #{$sidebar-logo-height + $sidebar-user-height + $sidebar-appmenu-height});
         max-height: calc(100vh - #{$sidebar-logo-height + $sidebar-user-height + $sidebar-appmenu-height});
         .track-vertical {
            right: 1px;
            top: 0;
            height: 100%;
            opacity: 0;
            background: rgba($color: #383e54, $alpha: 0.3);
            .thumb-vertical {
               background: rgba($qiplus-color, .9);
               border-radius: 3px;
               box-shadow: 0 0 0 2px #383e54;
            }
         }
         &:hover{
            .track-vertical {
               opacity: 1;
               background: rgba($color: #383e54, $alpha: 0.7);
            }
         }
      }
   }
   .rct-sidebar-nav {
      margin-top: 1rem;
      .rct-mainMenu {
         li {
            &.side-title {
               font-size: 0.75rem;
               line-height: 1.25rem;
               position: static;
               padding: 0.5rem 1.5rem !important;
            }
            .menu-icon {
               min-width:auto;
               margin-right:1rem;
               font-size: 1.25rem;
               .ti-dashboard {
                  font-size: 25px;
               }
            }
            &.list-item {
               position: relative;
               span.menu {
                  font-size: 0.875rem;
               }
               &:after {
                  position: absolute;
                  content: "\f2fb";
                  font-family: Material-Design-Iconic-Font;
                  right: 1.5rem;
                  top: 0;
                  bottom: 0;
                  display: flex;
                  align-items: center;
                  transform: rotate(0);
                  transition: all 0.2s ease-in-out;
               }
               &.item-active {
                  background-color: rgba(0, 0, 0, 0.2);
                  span.menu {
                     font-weight: $fw-semi-bold;
                  }
                  &:after {
                     transform: rotate(90deg);
                  }
               }
            }
         }
         > li.item-solo.item-current,
         > li.list-item.item-current {
            background-color: $qiplus-color;
            ~ .sub-menu li.item-current{
               background: rgba(0,0,0,0.2);
               border-left: 2px solid $qiplus-color;
            }
         }
         >li:not(.side-title) {
            padding: 1rem 1.5rem;
         }
         >li:not(.list-item) {
            padding: 0;
            display: flex;
            a {
               display: flex;
               align-items: center;
               padding: 1rem 1.5rem;
            }
         }
         .sub-menu {
            ul {
               li {
                  padding: 0;
                  display: flex;
                  a {
                     display: flex;
                     align-items: center;
                     min-height: 50px;
                     padding: 0.75rem 1.5rem 0.75rem 3.4rem;
                     padding-left: 2.2rem;
                     flex: 1;
                     &.item-active {
                        font-weight: $fw-semi-bold;
                     }
                  }
                  &.list-item {
                     padding: 0.75rem 1.5rem 0.75rem 3.4rem;
                     padding-left: 2.2rem;
                  }
                  &.submenu-navlink {
                     > a {
                        padding-left: 2rem;
                     }
                  }
               }
            }
            ul li a,
            ul li.list-item .menu-icon,
            ul li.list-item span.menu {
               font-size: 0.85rem;
            }
         }
      }
      nav.app-navigation {
         /* 
         position: fixed;
         bottom: 70px;
         left: 0;
         width: 100%;
         z-index: 100;
         background: #383e54; 
         */
         position: fixed;
         bottom: 75px;
         left: 0;
         width: 100%;
         z-index: 100;
         overflow: hidden;
         border-top: 3px double #383e54;
         border-bottom: 3px double #383e54;
         padding-bottom: 0.725em;
         padding-top: 5px;
         &:before {
            // content: '';
            background: #464D69 linear-gradient(180deg, #586184, #464D69) repeat-x;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 15px;
            display: block;
         }
         li a {
            display: flex;
            align-items: center;
            white-space: nowrap;
            i {
               font-size: 20px;
               display: flex;
               width: 30px;
               justify-content: center;
            }
            span {
               margin-left: 5px;
               font-size: 14px;
               &.badge {
                  transform: scale(0.8);
                  left: 24px;
                  top: 4px;
                  display: flex !important;
               }
            }
         }
      }
   }
}

.sidebar-user {
   position: absolute;
   z-index: 1400;
   width: 100%;
   bottom: 0;
   left: 0;
   max-width: $sidebar-width;
   transition: all 0.3s ease-out 0s;
   .sidebar-user-block {
      padding: 1rem 0; //20px 0;
      display: flex;
      justify-content: center;
      .user-info {
         color: $white;
      }
      .rct-dropdown {
         cursor: pointer;
         .dropdown-menu {
            background-color: $base-light;
            min-width: 200px;
            max-width: $sidebar-width;
            box-shadow: 0 0 10px;
            &.show {
               top: -1.25rem !important;
               left: -1.25rem !important;
            }
            .user-profile-top {
               background: $qiplus-color !important;
            }
            ul {
               background-color: transparent;
               li:not(.user-profile-top) {
                  box-shadow: 0 3px 2px 0 rgba(0, 0, 0, 0.02);
                  margin: 0.3125rem;
                  a {
                     background-color: $white;
                     padding: 1rem 1.4rem 1rem 0.6rem;
                     line-height: 1.3;
                     display: flex;
                     align-items: center;
                     i {
                        font-size: 1rem;
                     }
                     span:not(.badge) {
                        color: $body-color;
                        font-size: $font-size-sm;
                        transition: color 0.3s ease;
                     }
                     &:hover {
                        span:not(.badge) {
                           color: $primary;
                        }
                     }
                  }
               }
            }
         }
      }
   }
   .user-profile {
      width: 45px;
      height: 45px;
      >.MuiAvatar-root {
         background: $qiplus-color !important;
      }
      >img {
         box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.5);
         object-fit: cover;
         height: 100%;
         width: 100%;
         border: 2px solid #464d69;
      }
   }
   .session-account-title {
      width: 100%;
      max-width: 100%;
      z-index: 10;
      bottom: 0px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: block;
      position: absolute;
      text-align: center;
      padding: 1px 3px;
   }     
}


.dropdown {
   .dropdown-menu {
      transform: translate3d(0px, 50px, 0px);
      padding: 0;
   }
}

.rct-sidebar {
   .sidebar-overlay-dark {
      * {
         color: $white;
      }
   }
   .sidebar-overlay-light {
      * {
         color: $sidebar-link-font-color;
      }
      .site-logo{
         background-color: $primary;
      }
   }
}

.collapsed-sidebar {
   .rct-header {
      left: 0;
   }
   .rct-sidebar {
      width: 0;
   }
   .rct-app-content {
      width: 100%;
   }
}

/*========= Customizer ==========*/

.rct-customizer {
   width: $sidebar-width;
   overflow-x: hidden;
   header {
      background: $gray-900;
      color: $white;
   }
   .chat-area {
      .chat-head {
         @include border (1px solid, $border-color, top);
      }
      .chat-head,
      .chat-body {
         padding: 0.625rem; //10px;
      }
      .chat-body {
         .media-body {
            padding: 0.625rem !important;
            margin: 3px;
         }
      }
      .attachment {
         align-items: center;
         display: flex;
      }
      h3 {
         font-size: 14px;
         margin: 0;
      }
      .send-icons {
         ul {
            >li {
               &:nth-last-of-type(-n+2) {
                  display: none;
               }
            }
         }
      }
      .chat-footer {
         padding: 0;
      }
   }
   .chat-sidebar {
      .badge-xs {
         padding: 5px;
      }
      .media-body {
         h5 {
            font-size: 14px;
         }
      }
   }
   .panel-title {
      font-size: 14px;
      font-weight: bold;
      margin: 10px;
   }
}

@media(max-width:1199px){
   .rct-sidebar-wrap,.rct-page{
      .rct-scroll{
         >div:first-child{
            margin-bottom: 0 !important;
            padding-bottom: 65px !important;
         }
      }
   }
   .rct-page{
      .Crm-wrapper .rct-scroll{
         >div:first-child{
            padding-bottom: 10px !important;
         }
      }
   }
   .app-boxed{
      .rct-page{
         .rct-scroll{
            >div:first-child{
               padding-bottom: 0px !important;
            }
         }
      }
   }
}
.new-item{
   height:20px !important;
   background-color:#e53935 !important;
   position: absolute;   
   right:40px;
   span{
      padding: 0 9px !important;
      color:#fff !important;
   }
   &:hover{
      span{
         color: #fff !important;
      }
   }
}
.app-main-container > div:nth-child(2) {
   > div:first-child{
      z-index: 1210 !important;
   }
   > div:nth-child(2){
      z-index: 1209 !important;
   }
}