/*============ Editor Style =============*/
// Wysiwyg Editor
.rct-editor-wrapper {
	.rdw-editor-toolbar {
		border: 1px solid $input-border-color;
		// background-color: $blue;
	}
	.rct-wysiwyg-editor {
		min-height: 300px;
		border: 1px solid $input-border-color;
		padding: 5px 15px;
		background: $block-bg;
	}
}

// Quill Ediotr
.editor-wrapper {
	background-color: $white;
}
.quill {
	overflow: hidden;
}
.quill, .ql-editor {
	height: 100%;
	box-sizing: border-box;
}
.ql-container {
	min-height: 100px;
}
.ql-container.ql-snow, .ql-toolbar.ql-snow {
	border-color: $input-border-color;
}

.ql-editor {
	font-size: 16px;
	p, h1, h2, h3, h4, h5, h6 {
		margin-bottom: 0.5em;
	}
	p {
		font-size: inherit;
	}
}

body[data-section] {
	.rct-editor-wrapper {
		background: #f9f9f9;
		.rdw-editor-toolbar {
			border: 1px solid $input-border-color;
		}
		.rct-wysiwyg-editor {
			max-width: 640px;
			margin: 0 auto;
			border-left: 1px solid #f1f1f1;
			border-right: 1px solid #f1f1f1;
			background: $white;
			padding: 40px 40px;
		}
	}
	
	.ql-container {
		background: #f9f9f9;
	}
	.ql-editor {
		max-width: 640px;
		margin: 0 auto;
		border-left: 1px solid #f1f1f1;
		border-right: 1px solid #f1f1f1;
		background: $white;
		padding: 40px 40px;
	}
}

/*============ ckEditor Styles =============*/
body[data-section] {
	.ckeditor-wrapper {
		min-height: 300px;
		.cke,
		.cke_inner{
			height: 100%;
			min-height: 300px;
			.cke_contents {
				background: #f9f9f9;
				height: calc(100% - 102px);
				min-height: calc(100% - 102px);
				.cke_wysiwyg_frame {
					max-width: 840px;
					margin: 0 auto;
					display: block;
				}
			}
		}
	}
	.cke_dialog_container {
		.cke_dialog_body {
			border-radius: 6px;
			overflow: hidden;
			min-width: 800px;
			box-shadow: 0 0 20px rgba(0,0,0,0.3);
			max-width: 96%;
			.cke_dialog_contents {
				width: 100%;
				a.cke_dialog_ui_button {
					line-height: 36px;
					border-radius: 5px;
					display: inline-block;
					background: #e4e4e4;
					color: #666;
					> span {
						color: #666;
					}
				}
				.cke_dialog_contents_body {
					padding: 10px 15px;
					input.cke_dialog_ui_input_text, 
					input.cke_dialog_ui_input_password, 
					input.cke_dialog_ui_input_tel, 
					select.cke_dialog_ui_input_select,
					textarea.cke_dialog_ui_input_textarea {
						font-size: .9rem;
						border-radius: 5px;
						line-height: 1.3;
						padding: 3px 10px;
						min-height: 46px;
					}			
					label {
						display: block;
						font-size: .75rem;
						margin-bottom: 5px;
						width: auto !important;
						padding: 0 5px;
					}
					.cke_dialog_ui_labeled_content.cke_dialog_ui_input_file {
						margin-top: 23px !important;
					}				
					a.cke_dialog_ui_fileButton.cke_dialog_ui_button {
						margin-top: 30px;
					}
					.cke_dialog_image_url a.cke_dialog_ui_button {
						margin-top: 23px !important;
					}
					td.cke_dialog_ui_hbox_last {
						vertical-align: top !important;
					}
					td.cke_dialog_ui_hbox_last .cke_dialog_ui_vbox {
						height: 100%;
						min-height: 100%;
					}				
					td.cke_dialog_ui_hbox_last .cke_dialog_ui_vbox .ImagePreviewBox {
						height: 100%;
						min-height: 340px;
						width: 600px;
					}
					td.cke_dialog_ui_hbox_first {
						input[type="text"],
						input[type="number"],
						input[type="url"],
						input[type="email"],
						select {
							min-width: 100px !important;
						}
						.cke_dialog_image_ratiolock {
							float: right;
							display: flex;
							flex-direction: column-reverse;
							align-items: center;
							margin-top: 52px !important;
							a.cke_btn_reset,
							a.cke_btn_locked {
								background: none;
								width: 22px;
								height: 22px;
								border: 0;
								margin: 0;
							}
							a.cke_btn_locked {
								margin-top: 43px;
							}
							a.cke_btn_reset:after,
							a.cke_btn_locked:after {
								display: inline-block;
								font: normal normal normal 14px/1 FontAwesome;
								font-size: 20px;
								text-rendering: auto;
								-webkit-font-smoothing: antialiased;
								-moz-osx-font-smoothing: grayscale;
							}
							a.cke_btn_locked:after {
								content: "\f023";
							}
							a.cke_btn_unlocked:after {
								content: "\f09c";
							}
							a.cke_btn_reset:after {
								content: "\f0e2";
							}
						}
					}
				}
				.cke_dialog_footer {
					a.cke_dialog_ui_button {
						min-width: 80px;
					}
					a.cke_dialog_ui_button_ok {
						min-width: 80px;
						color: $white;
						border-color: $primary;
						background: $primary;
						text-shadow: 0 0 2px rgba(0,0,0,0.5);
						> span {
							color: $white;
						}
					}
					.cke_resizer {
						margin-top: 42px;
					}
				}
			}
		}
	}
}