/*============ Warnings Styles =============*/

// Estilos específicos para a funcionalidade de Warnings
.warnings-detail {
  .editor-container {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box;
    
    .ckeditor-wrapper,
    .simple-editor-wrapper {
      width: 100% !important;
      max-width: 100% !important;
      border: none !important;
      
      .cke {
        width: 100% !important;
        max-width: 100% !important;
        border: none !important;
        box-shadow: none !important;
        
        .cke_inner {
          width: 100% !important;
          max-width: 100% !important;
          
          .cke_contents {
            width: 100% !important;
            max-width: 100% !important;
            
            .cke_wysiwyg_frame {
              width: 100% !important;
              max-width: 100% !important;
              margin: 0 !important;
            }
          }
        }
      }
    }
  }
  
  // Estilos para as abas
  .MuiTabs-root {
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
    
    .MuiTab-root {
      min-width: 120px;
      font-weight: 500;
      text-transform: none;
      
      &.Mui-selected {
        color: #5d78ff;
        font-weight: 600;
      }
    }
    
    .MuiTabs-indicator {
      background-color: #5d78ff;
      height: 3px;
    }
  }
  
  // Responsividade
  @media (max-width: 768px) {
    .editor-container {
      .ckeditor-wrapper,
      .simple-editor-wrapper {
        .cke {
          .cke_top {
            overflow-x: auto;
          }
        }
      }
    }
  }
}

// Fix global para CKEditor em containers responsivos
body[data-section] {
  .rct-block-content {
    .editor-container {
      .ckeditor-wrapper,
      .simple-editor-wrapper {
        width: 100% !important;
        max-width: 100% !important;
        
        .cke {
          width: 100% !important;
          max-width: 100% !important;
          
          .cke_inner {
            width: 100% !important;
            
            .cke_contents {
              width: 100% !important;
              
              .cke_wysiwyg_frame {
                width: 100% !important;
                max-width: 100% !important;
              }
            }
          }
        }
      }
    }
  }
}

// Estilos para cards de warnings na listagem
.warning-card-content {
  .warning-meta {
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
    margin-top: 15px;
    
    small {
      display: block;
      margin-bottom: 8px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      i {
        width: 16px;
        text-align: center;
        opacity: 0.7;
      }
    }
  }
  
  .warning-actions {
    .btn {
      font-size: 0.875rem;
      padding: 6px 12px;
      
      i {
        font-size: 0.75rem;
      }
    }
  }
}

// Badge de status
.badge {
  &.badge-success {
    background-color: #28a745;
  }
  
  &.badge-secondary {
    background-color: #6c757d;
  }
}
