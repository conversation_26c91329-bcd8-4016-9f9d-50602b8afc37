/**
 * Warning Model
 */
import moment from 'moment'
import { MOMENT_ISO } from '../../../../constants/AppConstants'

export const warningModel = {
  id: '',
  ID: '',
  title: '',
  content_mail: '',
  content_app: '',
  
  date: moment().format(MOMENT_ISO),
  modified: moment().format(MOMENT_ISO),
  
  collection: 'warnings',
  post_type: 'warning',
  
  active: true,
  author: '',
  owner: '',
  
  status: 'publish',
}

// Mock data for development
export const mockWarnings = [
  {
    id: '1',
    ID: '1',
    title: 'Manutenção Programada do Sistema',
    content_mail: 'Informamos que haverá uma manutenção programada no sistema QIPlus no dia 15/07/2025 das 02:00 às 04:00. Durante este período, alguns serviços podem ficar indisponíveis.',
    content_app: 'Manutenção programada: 15/07/2025 das 02:00 às 04:00. Alguns serviços podem ficar temporariamente indisponíveis.',
    date: moment().subtract(2, 'days').format(MOMENT_ISO),
    modified: moment().subtract(1, 'day').format(MOMENT_ISO),
    collection: 'warnings',
    post_type: 'warning',
    active: true,
    author: 'webmaster',
    owner: 'webmaster',
    status: 'publish',
  },
  {
    id: '2',
    ID: '2',
    title: 'Nova Funcionalidade: Relatórios Avançados',
    content_mail: 'Temos o prazer de anunciar o lançamento da nova funcionalidade de Relatórios Avançados. Esta ferramenta permitirá análises mais detalhadas dos seus dados de marketing e vendas.',
    content_app: 'Nova funcionalidade disponível: Relatórios Avançados! Acesse o menu Relatórios para conhecer as novas opções.',
    date: moment().subtract(5, 'days').format(MOMENT_ISO),
    modified: moment().subtract(3, 'days').format(MOMENT_ISO),
    collection: 'warnings',
    post_type: 'warning',
    active: true,
    author: 'admin',
    owner: 'admin',
    status: 'publish',
  },
  {
    id: '3',
    ID: '3',
    title: 'Atualização de Segurança Importante',
    content_mail: 'Uma importante atualização de segurança foi aplicada ao sistema. Recomendamos que todos os usuários façam logout e login novamente para garantir que as melhorias sejam aplicadas.',
    content_app: 'Atualização de segurança aplicada. Faça logout e login novamente para aplicar as melhorias.',
    date: moment().subtract(7, 'days').format(MOMENT_ISO),
    modified: moment().subtract(6, 'days').format(MOMENT_ISO),
    collection: 'warnings',
    post_type: 'warning',
    active: false,
    author: 'webmaster',
    owner: 'webmaster',
    status: 'publish',
  },
  {
    id: '4',
    ID: '4',
    title: 'Integração com WhatsApp Business API',
    content_mail: 'Agora você pode integrar sua conta do WhatsApp Business API diretamente com o QIPlus. Esta integração permite automatizar mensagens e melhorar o atendimento ao cliente.',
    content_app: 'Nova integração disponível: WhatsApp Business API. Configure agora no menu Integrações.',
    date: moment().subtract(10, 'days').format(MOMENT_ISO),
    modified: moment().subtract(8, 'days').format(MOMENT_ISO),
    collection: 'warnings',
    post_type: 'warning',
    active: true,
    author: 'admin',
    owner: 'admin',
    status: 'publish',
  },
  {
    id: '5',
    ID: '5',
    title: 'Política de Privacidade Atualizada',
    content_mail: 'Nossa Política de Privacidade foi atualizada para estar em conformidade com as mais recentes regulamentações de proteção de dados. Recomendamos a leitura das alterações.',
    content_app: 'Política de Privacidade atualizada. Acesse as configurações da conta para mais detalhes.',
    date: moment().subtract(15, 'days').format(MOMENT_ISO),
    modified: moment().subtract(12, 'days').format(MOMENT_ISO),
    collection: 'warnings',
    post_type: 'warning',
    active: true,
    author: 'webmaster',
    owner: 'webmaster',
    status: 'publish',
  }
]

module.exports = { warningModel, mockWarnings }
