/**
 * Rct Card
 */
import React from 'react'

// rct card heading
import { RctCardHeading } from './RctCardHeading'

const RctCard = ({ children, heading, headingClasses, colClasses, customClasses, square }) => (
  <div className={`rct-block-wrap ${colClasses || ''}`}>
    <div className={`rct-block ${customClasses ? customClasses : ''} ${square ? 'radius-0' : ''}`}>
      {heading && <RctCardHeading title={heading} customClasses={headingClasses} />}
      {children}
    </div>
  </div>
)

export { RctCard }
