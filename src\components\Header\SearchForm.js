/**
 * Search Form
 */
import React from 'react'
import { Input } from 'reactstrap'
import { langMessages } from '../../lang'

export default function SearchForm(props) {
  return (
    <div className="search-wrapper">
      <Input
        type="search"
        className={`search-input-lg ${props.className || ''}`}
        placeholder={props.placeholder || `${langMessages['widgets.search']}...`}
        onChange={props.onChange}
      />
    </div>
  )
}
