import React, { Component } from 'react'

import { Responsive<PERSON>ontainer, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Legend, Tooltip } from 'recharts'

import ChartConfig from 'Constants/chart-config'

const BarChartComponent = ({ datasets, data, colors, showLegend, showTooltip }) => {
  let configColors = Object.keys(ChartConfig.color)
  let colorsIndex = 0

  let bars = []
  let chartData = []

  if (Array.isArray(datasets)) {
    datasets.forEach((set, k) => {
      let { name, label, value, stackId, color } = set
      let index = chartData.findIndex(d => d.name === name)
      if (index > -1) {
        chartData[index][label] = value
      } else {
        chartData.push({ name, [label]: value })
      }
      if (!bars.find(b => b.dataKey === label)) bars.push({ dataKey: label, fill: color, stackId })
    })
  } else {
    chartData = data.map((set, k) => {
      Object.keys(set).forEach((dataKey, index) => {
        if (dataKey !== 'name' && dataKey !== 'color') {
          if (k === 0) {
            let color = colors[dataKey]
            if (!color) {
              color = ChartConfig.color[configColors[colorsIndex]]
              colorsIndex++
              if (colorsIndex >= configColors.length) {
                colorsIndex = 0
              }
            }
            bars.push({ dataKey, fill: color })
          }
        }
      })
      return set
    })
  }

  let maxVal = chartData.reduce((all, b) => {
    let v = Object.keys(b).reduce((bAll, k) => (b[k] > bAll ? b[k] : bAll), all)
    return v > all ? v : all
  }, 1)

  ;[1, 10, 100, 1000, 10000, 100000, 1000000].forEach(v => (maxVal = maxVal + (maxVal > v ? v - (maxVal % v) : 0)))

  // console.log('BarChartComponent',{chartData, bars, datasets, data, maxVal});

  const CustomTooltip = ({ active, payload, label, ...other }) => {
    // console.log('CustomTooltip',{active, payload, label, other});
    if (active && payload && payload.length) {
      return (
        <React.Fragment>
          <small className="custom-chart-tooltip-label">{label}</small>
          {payload.map((p, pIndex) => {
            let bar = bars.find(b => b.dataKey === p.dataKey)
            if (!bar) return null
            return (
              <div className="custom-chart-tooltip" key={pIndex}>
                <small className="custom-chart-tooltip-item" style={{ color: bar.fill }}>{`${bar.dataKey} : ${p.value}`}</small>
              </div>
            )
          })}
        </React.Fragment>
      )
    }
    return null
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={chartData} barGap={0} barSize={20} margin={{ top: 10, right: 5, bottom: 10, left: 15 }}>
        <XAxis dataKey="name" stroke={ChartConfig.axesColor} tickLine={false} />
        <YAxis stroke={ChartConfig.axesColor} domain={[0, maxVal]} minTickGap={0} tickLine={false} width={30} interval={0} axisLine={false} />
        <CartesianGrid vertical={false} stroke={ChartConfig.chartGridColor} />
        {!!showLegend && <Legend />}
        {!!showTooltip && <Tooltip className="BarChart-custom-tooltip custom-tooltip" content={<CustomTooltip />} />}
        {bars.map((bar, k) => {
          return <Bar {...bar} key={k} />
        })}
      </BarChart>
    </ResponsiveContainer>
  )
}

export default BarChartComponent
