import React, { useEffect, useState } from "react";
import CountUp from "react-countup";
import ReportsGraph from "./ReportsGraph";

const ReportsWidgets = (props) => {
  const { data, showGraph, onClick } = props;

  const [dataWidgets, setDataWidgets] = useState([]);
  const [dataGraph, setDataGraph] = useState(data);

  useEffect(() => {
    if (data.length > 0) {
      let arrData = [];
      // data.forEach((item) => {
      //   if (item.period === "day") {
      //     arrData.push(item);
      //   }
      // });
      setDataWidgets(
        data.filter(
          (item) =>
            item.showOnReports === true || item.showOnReports === undefined
        )
      );
    } else {
      setDataWidgets([]);
    }
    setDataGraph(
      data.filter(
        (item) =>
          item.showOnReports === true || item.showOnReports === undefined
      )
    );
    onClick([]);
  }, [data]);

  function handleDataGraph(metric = "") {
    if (metric === "") {
      return;
    }
    const dataResult = data.find((item) => item.name === metric);
    onClick(dataResult);
    setDataGraph([dataResult]); // update data
  }

  return (
    <>
      <div className="report-facebook-status">
        <ul className="list-inline d-flex align-content-center">
          {dataWidgets.length > 0 &&
            dataWidgets?.map((item) => (
              <li
                key={item.name}
                className="list-inline-item col"
                onClick={() => handleDataGraph(item.name)}
              >
                <h4>
                  {/* <span className={`ladgend `}>
                    <Tooltip title={item.description}>

                    <i className="material-icons text-white">zoon_in</i>

                    </Tooltip>
                  </span>{" "} */}
                  {item.title}
                </h4>
                <h2 className="title">
                  <CountUp start={0} end={Number(item.sum)} duration={3} />{" "}
                  &nbsp;
                  {/* <small>
                        <small className="text-base float-right">
                          <small>
                            {langMessages["stats.uniqueVisitors"].toLowerCase()}
                          </small>
                        </small>
                      </small> */}
                </h2>
              </li>
            ))}
        </ul>
      </div>
      {showGraph && (
        <div>
          <ReportsGraph
            title="Visão Geral"
            legend={true}
            responsive={false}
            data={dataGraph}
          />
        </div>
      )}
    </>
  );
};

export default ReportsWidgets;
