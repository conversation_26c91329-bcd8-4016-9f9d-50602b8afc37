/**
 * Dasboard Routes
 */
import React from 'react'
import { Route, Switch } from 'react-router-dom'

// async components
import { AsyncModules } from '../../components/AsyncComponent'
import { FinancesDashboard } from './finances'
import { WarningsList } from './warnings/list'

const Dashboard = ({ match }) => (
  <div className="dashboard-wrapper">
    <Switch>
      {/* <Redirect exact from={`${match.url}/`} to={`${match.url}/dashboard`} /> */}
      <Route exact path={`${match.path}`} component={AsyncModules[match.url.replace(/\//g, '')].list} />
      <Route exact path={`${match.path}/finances`} component={FinancesDashboard} />
      <Route exact path={`${match.path}/warnings`} component={WarningsList} />
      <Route exact path={`${match.path}/add`} component={AsyncModules[match.url.replace(/\//g, '')].detail} />
      <Route exact path={`${match.path}/:postId`} component={AsyncModules[match.url.replace(/\//g, '')].detail} />
    </Switch>
  </div>
)

export default Dashboard
