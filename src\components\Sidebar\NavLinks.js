import COLLECTIONS from '../../constants/AppCollections'
import AppModules from '../../constants/AppModules'
import ROLES, { ADMIN_LEVEL, MANAGER_LEVEL, OWNER_LEVEL } from '../../constants/UsersRoles'
import { isCurrentMenu } from '../../helpers/helpers'

// Get current language
const userLocale = localStorage.getItem('config.locale') || navigator?.language?.split('-')?.[0] || 'en'
const lang = ['pt', 'en', 'es'].includes(userLocale) ? userLocale : 'en'

const isOpen = menu => isCurrentMenu(menu)

const menuGroups = {
  dashboard: {
    label: 'menu.dashboard',
    icon: 'ti-dashboard',
    child_routes: [
      {
        path: '/dashboard',
        menu_title: 'sidebar.dashboard',
        menu_icon: 'ti-dashboard',
        menu_level: 0,
        new_item: false,
        cap: '',
        exact: true,
      },
    ],
  },
  deals: {
    label: 'menu.deals',
    icon: 'fa fa-handshake-o',
  },
  contacts: {
    label: 'menu.contacts',
    icon: 'icon-people',
  },
  events: {
    label: 'menu.events',
    icon: 'icon-globe',
  },
  stores: {
    label: 'menu.stores.singular',
    icon: 'icon-basket',
  },
  campaigns: {
    label: 'menu.campaigns',
    icon: 'ti-announcement',
  },
  mailing: {
    label: 'sidebar.mailing',
    icon: 'fa fa-paper-plane-o',
  },
  tools: {
    label: 'menu.tools',
    icon: 'icon-briefcase',
    level: MANAGER_LEVEL,
  },
  shotx: {
    label: 'menu.shotx',
    icon: 'icon-briefcase',
    level: MANAGER_LEVEL,
  },
  reports: {
    label: 'menu.reports',
    icon: 'fa fa-line-chart',
    level: MANAGER_LEVEL,
    child_routes: [
      {
        path: '/reports/deals',
        menu_title: 'modules.deals',
        menu_icon: AppModules[COLLECTIONS.DEALS_COLLECTION_NAME].icon,
        menu_level: ROLES.MANAGER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      },
      {
        path: '/reports/teams',
        menu_title: 'modules.teams',
        menu_icon: AppModules[COLLECTIONS.TEAMS_COLLECTION_NAME].icon,
        menu_level: ROLES.OWNER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      },
      {
        path: '/reports/integrations/googleads',
        menu_title: 'sidebar.reports.automations.ads',
        menu_icon: AppModules[COLLECTIONS.INTEGRATIONS_COLLECTION_NAME].icon,
        menu_level: ROLES.OWNER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      },
      // TODO: habilitar apos aprovacao da meta
      {
        path: '/reports/integrations/facebookads',
        menu_title: 'components.facebookAds',
        menu_icon: AppModules[COLLECTIONS.INTEGRATIONS_COLLECTION_NAME].icon,
        menu_level: ROLES.OWNER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      },
    ],
  },
  users: {
    label: 'menu.users',
    icon: 'ti-id-badge',
    level: OWNER_LEVEL,
  },
  plans: {
    label: 'menu.plans',
    icon: 'icon-rocket',
    level: ADMIN_LEVEL,
    child_routes: [
      {
        path: '/qiplus-plans/finances',
        menu_title: 'sidebar.plans.dashboard',
        menu_icon: 'fa fa-line-chart',
        menu_level: ROLES.WEBMASTER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      },
      {
        path: '/qiplus-plans/warnings',
        menu_title: 'sidebar.plans.warnings',
        menu_icon: 'ti-announcement',
        menu_level: ROLES.WEBMASTER_LEVEL,
        new_item: false,
        cap: '',
        exact: true,
      }
    ]
  },
}

const navLinks = {}

Object.keys(menuGroups).forEach(groupKey => {
  const menuGroup = menuGroups[groupKey]

  navLinks[groupKey] = [
    {
      menu_title: menuGroup.label,
      menu_icon: menuGroup.icon,
      menu_level: menuGroup.level || 0,
      child_routes: menuGroup.child_routes || [],
    },
  ]
})

Object.keys(AppModules).forEach(collection => {
  const AppModule = AppModules[collection]
  const groupKey = AppModule.menu_group

  if (groupKey && navLinks[groupKey]) {
    // const menuIndex = navLinks[groupKey].findIndex(m => m.menu_title === `sidebar.${groupKey}`);
    const menuIndex = 0
    const route = AppModule.route || collection
    const caps = AppModule.caps || null

    const menuItem = {
      caps,
      collection,
      module: AppModule.module,
      menu_title: `menu.${collection}`,
      menu_icon: AppModule.icon,
      menu_level: AppModule.level || 0,
      new_item: !!AppModule.new_item,
      path: `/${route}`,
    }

    if (!('menu_list' in AppModule) || AppModule.menu_list !== false) {
      menuItem.child_routes = []
      menuItem.child_routes.push({
        path: `/${route}/`,
        new_item: false,
        menu_title: 'sidebar.listAction',
        cap: 'list',
        exact: true,
      })
      menuItem.child_routes.push({
        path: `/${route}/add`,
        new_item: false,
        menu_title: 'sidebar.addNew',
        cap: 'add',
        exact: true,
      })
    }

    if (AppModule.sub_menu) {
      Object.keys(AppModule.sub_menu).forEach(key => {
        const subMenu = AppModule.sub_menu[key]
        !menuItem.child_routes && (menuItem.child_routes = [])
        subMenu.subheader && (menuItem.subheader = subMenu.subheader)
        menuItem.child_routes.push({
          path: `${subMenu.path}`,
          new_item: !!subMenu.new_item,
          menu_icon: subMenu.icon || '',
          menu_title: subMenu.label,
          level: subMenu.level || 0,
          cap: subMenu.cap || '',
          exact: true,
          redirect: subMenu.redirect
        })
      })
    }

    navLinks[groupKey][menuIndex]['type_multi'] = true
    navLinks[groupKey][menuIndex].child_routes.push(menuItem)
    navLinks[groupKey][menuIndex]['open'] = isOpen(navLinks[groupKey][menuIndex])
  }
})

Object.keys(navLinks).forEach(groupKey => {
  const menuIndex = 0
  const menuGroup = navLinks[groupKey][menuIndex]
  const { child_routes } = menuGroup

  if (groupKey !== 'app' && child_routes.length === 1) {
    navLinks[groupKey][menuIndex] = child_routes[0]
  }

  // if( window.location.search ) {
  //    const queryString = window.location.search;
  //    menuGroup.child_routes = child_routes.map(c=>{
  //       if(c.child_routes) c.child_routes = c.child_routes.map(cR=>({ ...cR, path: cR.path+queryString }))
  //       return { ...c, path: c.path+queryString }
  //    })
  // }
})

// console.log('navLinks',navLinks);
export default navLinks
