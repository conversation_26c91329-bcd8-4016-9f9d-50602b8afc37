// qiplus
$xxlClass: 'col-xxl-';
$xxlOffset: 'offset-xxl-';
$radiusClass: 'radius-';
$radiusTopLeftClass: 'radius-tl-';
$radiusTopRightClass: 'radius-tr-';
$radiusBottomLeftClass: 'radius-bl-';
$radiusBottomRightClass: 'radius-br-';
$topClass: 'top-';
$rightClass: 'right-';
$bottomClass: 'bottom-';
$leftClass: 'left-';
$lineClass: 'line-';
$zIndexClass: 'z-';
$flexClass: 'flex-';
$bdClass: 'bd-';
$easeClass: 'ease-';
$opacityClass: 'op-';
$flexColsClass: 'flex-cols-';
$hoverScaleClass: 'hover-scale-';
$activeScaleClass: 'active-scale-';

// loops through 10 times
$limit: 20;
@for $i from 0 through $limit {
    
    .#{$radiusClass}#{$i} {
        border-radius: #{$i * 1px} !important;
    }
    .#{$radiusTopLeftClass}#{$i} {
        border-top-left-radius: #{$i * 1px} !important;
    }
    .#{$radiusTopRightClass}#{$i} {
        border-top-right-radius: #{$i * 1px} !important;
    }
    .#{$radiusBottomLeftClass}#{$i} {
        border-bottom-left-radius: #{$i * 1px} !important;
    }
    .#{$radiusBottomRightClass}#{$i} {
        border-bottom-right-radius: #{$i * 1px} !important;
    }
    .#{$topClass}#{$i} {
        top: #{$i * 1px} !important;
    }
    .#{$rightClass}#{$i} {
        right: #{$i * 1px} !important;
    }
    .#{$bottomClass}#{$i} {
        bottom: #{$i * 1px} !important;
    }
    .#{$leftClass}#{$i} {
        left: #{$i * 1px} !important;
    }
    .#{$zIndexClass}#{$i} {
        z-index: #{$i};
    }
    .#{$bdClass}#{$i} {
        border-width: $i * 1px;
        border-style: solid;
    }
    .#{$easeClass}#{$i} {
        transition: ease ($i) * 0.1s;        
    }
    
    $decimalVal: $i / 10 + 1;
    $decimalClass: $decimalVal * 10;
    
    .#{$lineClass}#{$decimalClass} {
        line-height: $decimalVal;
    }
    .#{$hoverScaleClass}#{$decimalClass}:hover {
        transform: scale($decimalVal);
    }
    .#{$activeScaleClass}#{$decimalClass}:active {
        transform: scale($decimalVal);
    }
    .#{$easeClass}#{$decimalClass} {
        transition: ease ($decimalVal) * 1s;        
    }
    .#{$opacityClass}#{($decimalClass) - 10} {
        opacity: $decimalVal - 1;
    }
    
}

@for $i from 1 through 12 {
    
    .#{$flexClass}#{$i} {
        flex: #{$i};
    }
    
    $percent: 100/$i;
    
    // for each $flex-#{i} 
    .#{$flexColsClass}#{$i} {
        flex-basis: $percent * 1%;
        flex-grow: 0;
    }
    
}

@media (min-width: 1440px) {
    @for $i from 1 through 12 {
        .#{$xxlClass}#{$i} {
            flex: 0 0 (100 / 12 * $i * 1%);
            max-width: (100 / 12 * $i * 1%);
        }
    }
    @for $i from 1 through 11 {
        .#{$xxlOffset}#{$i} {
            margin-left: (100 / 12 * $i * 1%);
        }
    }
    .#{$xxlOffset}0 {
        margin-left: 0;
    }
}

.flex-break {
    flex-basis: 100%;
    height: 0;
}

.bg-none {
    background: none !important;
}

.shadow-none {
    box-shadow: none !important;
}

.nowrap {
    white-space: nowrap !important;
}

.nowrap-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    max-width: 100%;
}

@for $i from 1 through 10 {
    .nowrap-ellipsis-#{$i}0 {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;    
        max-width: ($i * 10%);
    }
}

.pointer,
.cursor-pointer {
    cursor: pointer;
}

.draggable-item.static-copy ~ .draggable-item:not(.isDragging) {
    transform: none !important;
}

.DropdownMenu-small {
    transform: scale(0.8);
    transform-origin: top;
}

.css-tlfecz-indicatorContainer {
    cursor: pointer;
}

.lead-management {
    .table thead th:last-of-type, 
    .table tbody td:last-of-type {
        text-align: right;
        padding-right: 20px;
    }
}

// session > login|register|recover
.rct-session-wrapper {
    .session-inner-wrapper {
        .session-body {
            padding: 2rem 3.25rem;
        }
    }
}

@media (max-width: 767px) {
    .rct-session-wrapper {
        .session-inner-wrapper {
            .session-body {
                padding: 2rem 1.75rem;
            }
        }
    }
}