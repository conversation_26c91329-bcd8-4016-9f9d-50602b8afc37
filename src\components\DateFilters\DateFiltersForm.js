/**
 * Search Form Component
 */
import React from 'react'


// lang strings
import { langMessages } from '../../lang'

import DTPicker from '../../components/Widgets/DateTimePicker'

import { IconButton, InputAdornment, MenuItem, TextField } from '@material-ui/core'

import { isset, parseRange } from '../../helpers/helpers'

const defaultOptions = ['currentMonth', 'currentYear', 'last7Days', 'last15Days', 'last30Days', 'last90Days', 'last365Days']

const DateFiltersForm = ({ filters, onUpdate, ...props }) => {
  return (
    <div className={`top-filter d-flex ${props.wrapperClasses || ''}`}>
      {!!props.before && <div className="flex-1 w-xs-half-block p-5">{props.before}</div>}
      {'range' in filters && (
        <div className="flex-1 w-xs-half-block p-5">
          <Text<PERSON>ield
            select
            fullWidth
            size="small"
            variant="outlined"
            style={{ minWidth: 180 }}
            label={isset(props, ['labels', 'range']) ? props.labels.range || undefined : langMessages['dates.range']}
            value={filters.range}
            onChange={e => {
              const { start, end, range } = parseRange(e.target.value)
              onUpdate({ ...filters, start, end, range })
            }}
          >
            <MenuItem value={'none'}>{langMessages['texts.none']}</MenuItem>
            {(props.options || defaultOptions).map(p => (
              <MenuItem key={p} value={p}>
                {langMessages[`dates.${p}`] || p}
              </MenuItem>
            ))}
          </TextField>
        </div>
      )}
      {props.custom !== false && (
        <div className="flex-1 w-xs-half-block p-5">
          <DTPicker
            clearable
            inputVariant="outlined"
            size="small"
            value={filters.start}
            dateObj={true}
            inputProps={{
              startAdornment: <small className="mr-5">{langMessages['dates.from']}</small>,
              endAdornment: (
                <InputAdornment position="end" className="date-picker-icon">
                  <IconButton
                    onClick={e => {
                      e.stopPropagation()
                      onUpdate({ ...filters, range: '', start: '' })
                    }}
                    size="small"
                  >
                    <i className="fa fa-close" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            onChange={dateObj => {
              let end = filters.end
              if (end) {
                let updatedEnd = new Date(end)
                let duration = updatedEnd.getTime() - new Date(filters.start).getTime()
                updatedEnd.setTime(dateObj.getTime() + duration)
                end = updatedEnd.getTime()
              }
              onUpdate({ ...filters, range: '', start: dateObj.getTime(), end })
            }}
          />
        </div>
      )}
      {props.custom !== false && (
        <div className="flex-1 w-xs-half-block p-5">
          <DTPicker
            clearable
            inputVariant="outlined"
            size="small"
            value={filters.end}
            dateObj={true}
            inputProps={{
              startAdornment: <small className="mr-5">{langMessages['dates.until']}</small>,
              endAdornment: (
                <InputAdornment position="end" className="date-picker-icon">
                  <IconButton
                    onClick={e => {
                      e.stopPropagation()
                      onUpdate({ ...filters, range: '', end: '' })
                    }}
                    size="small"
                  >
                    <i className="fa fa-close" />
                  </IconButton>
                </InputAdornment>
              ),
            }}
            minDate={filters.start && new Date(filters.start)}
            onChange={dateObj => {
              onUpdate({ ...filters, range: '', end: dateObj.getTime() })
            }}
          />
        </div>
      )}
      {!!props.after && <div className="flex-1 w-xs-half-block p-5">{props.after}</div>}
    </div>
  )
}

export default DateFiltersForm
