import React, { PureComponent } from 'react'
import { <PERSON><PERSON>hart, Pie, Sector, ResponsiveContainer } from 'recharts'
import ChartConfig from '../../constants/chart-config'

const defaultHeight = 180

const renderActiveShape = props => {
  const RADIAN = Math.PI / 180
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props
  const { height, legends, externalLegends, externalSize, showLegend, iconSize, titleColor, titleSize, fontColor, fontSize, icon } = props // custom QIPlus
  const sin = Math.sin(-RADIAN * midAngle)
  const cos = Math.cos(-RADIAN * midAngle)
  const sx = cx + (outerRadius + 10) * cos
  const sy = cy + (outerRadius + 10) * sin
  const mx = cx + (outerRadius + 30) * cos
  const my = cy + (outerRadius + 30) * sin
  const ex = mx + (cos >= 0 ? 1 : -1) * 22
  const ey = my
  const ix = cx
  const iy = cy - 15
  const dy = externalLegends || !(legends || []).filter(d => !!d.name).length ? (titleSize || 16) / 2 : (-1 * (titleSize || 16)) / 2
  const textAnchor = cos >= 0 ? 'start' : 'end'
  return (
    <React.Fragment>
      <g>
        {!!icon && (
          <text
            x={ix}
            y={iy}
            textAnchor="middle"
            fontFamily="FontAwesome"
            fill={titleColor || fill}
            fontSize={iconSize || 20}
            ref={el => el && (el.innerHTML = `&#x${icon};`)}
          >
            {''}
          </text>
        )}
        <text x={cx} y={cy} dy={dy} textAnchor="middle" fill={titleColor || fill} fontSize={titleSize || 16}>
          {payload.name}
        </text>
        {!(percent === 1 && value === 1) && (
          <text x={cx} y={cy} dy={dy + 13} textAnchor="middle" fill={fontColor || fill} fontSize={fontSize || 11}>
            {`${(percent * 100).toFixed(2)}%`}
          </text>
        )}
        {!showLegend && !externalLegends && Array.isArray(legends) && !!legends.length && (
          <text x={cx} y={cy} dy={dy + 13 + 11} textAnchor="middle" fill={fontColor || fill} fontSize={8}>
            {`(${legends
              .filter(d => !!d.name)
              .map(d => `${d.name}: ${d.value}`)
              .join(', ')})`}
          </text>
        )}
        <Sector cx={cx} cy={cy} innerRadius={innerRadius} outerRadius={outerRadius} startAngle={startAngle} endAngle={endAngle} fill={fill} />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        {!!showLegend && !externalLegends && (
          <React.Fragment>
            <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
            <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
            <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333">{`${value}`}</text>
            <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={0} textAnchor={textAnchor} fill={fontColor || '#999'} fontSize={8}>
              {Array.isArray(legends) && !!legends.length
                ? `(${legends
                    .filter(d => !!d.name)
                    .map(d => `${d.name}: ${d.value}`)
                    .join(', ')})`
                : ''}
            </text>
          </React.Fragment>
        )}
        {!!externalLegends && Array.isArray(legends) && !!legends.length && (
          <text fill={fontColor || '#999'} x={cx} y={height || defaultHeight} textAnchor="middle" fontSize={externalSize || 11}>
            {`${legends
              .filter(d => !!d.name)
              .map(d => `${d.name}: ${d.value}`)
              .join(', ')}`}
          </text>
        )}
      </g>
    </React.Fragment>
  )
}

const data = [
  { name: 'Group A', value: 400 },
  { name: 'Group B', value: 300 },
  { name: 'Group C', value: 300 },
  { name: 'Group D', value: 200 },
]

export default class ActivePieChart extends PureComponent {
  state = {
    activeIndex: 0,
  }

  onPieEnter = (_, index) => {
    this.setState({ activeIndex: index })
  }

  render() {
    const { data, fill, width, height, innerRadius, outerRadius } = this.props
    // console.log('\nActivePieChart -----------------');
    return (
      <PieChart width={width || height || defaultHeight} height={height || width || defaultHeight}>
        <Pie
          activeIndex={this.state.activeIndex}
          activeShape={props => renderActiveShape({ ...props, ...this.props })}
          data={data}
          cx="50%"
          cy="50%"
          innerRadius={innerRadius || 60}
          outerRadius={outerRadius || 80}
          fill={fill || ChartConfig.color.qiplus}
          dataKey="value"
          onMouseEnter={this.onPieEnter}
        />
      </PieChart>
      // <ResponsiveContainer width={"100%"} height={"100%"}>
      // </ResponsiveContainer>
    )
  }
}
