/*====== Dark Layout Style Here ======*/
@media screen {

	body.dark-mode,
	body.dark-mode .app-boxed .rct-page {
		color: $dark-theme-text-color;
		background-color: $dark-bg-body;
		// background-image: $dark-layout-bg;
	}

	.dark-mode {

		.sidebar-color,
		.fixed-plugin .sidebar-overlay {
			display: none;
		}

		.loader-overlay {
			background-color: rgba(0, 0, 0, 0.8);
		}

		.sidebar-overlay-light {
			background-color: rgba(0, 0, 0, 0.8);
		}

		//main section
		.rct-header,
		.rct-footer,
		.rct-block-footer,
		.card-footer,
		.list-group-item,
		.report-title,
		.modal-content,
		input:not(.input-search),
		select,
		.rct-filter,
		.report-status,
		.button-nav-wrapper button,
		[role="document"],
		textarea,
		.listSection,
		.sweet-alert,
		.rct-wysiwyg-editor,
		.bg-light,
		.bg-light-yellow,
		.timeline-Widget,
		.horizontal-menu,
		.horizontal-menu .sub-menu,
		.horizontal-menu .sub-menu-child,
		.horizontal-menu .sub-menu-sub-child,
		table thead,
		.total-earn-chart .default-btn-group button,
		.dropdown-foot,
		.fixed-plugin .dropdown .dropdown-menu,
		.rct-mail-wrapper .list-wrap ul .list-item,
		.todo-wrapper .list-wrap ul .list-item,
		.rct-mail-wrapper .list-wrap .top-head,
		.todo-wrapper .list-wrap .top-head,
		.user-list-wrap>div>div,
		.chat-head,
		.page-title,
		.mail-list-wrap>div>div,
		.todo-list-wrap>div>div,
		.rct-sidebar .sidebar-user-block .rct-dropdown .dropdown-menu ul li:not(.user-profile-top) a {
			background-color: $dark-bg !important;
		}

		.quciklink-dropdown,
		.notification-dropdown,
		.language-dropdown,
		.cart-dropdown {
			.dropdown-menu .dropdown-list li {
				background-color: $dark-bg !important;

				&:hover {
					background-color: $dark-bg !important;
				}
			}
		}

		.horizontal-menu li:hover>a,
		.horizontal-menu .sub-menu li:hover>a,
		.horizontal-menu .sub-menu li .sub-menu-child li:hover>a,
		.table-hover tbody tr:hover,
		.quicklink-list,
		.aqua-ripple>div:hover,
		.social-card .social-icon,
		.chat-sidebar .chat-list ul .user-list-item.item-active,
		.dropzone-wrapper .dropzone {
			background-color: rgba(0, 0, 0, 0.2) !important;
		}

		.drawer-wrapper div,
		.comment-box,
		.card-base,
		.rct-session-wrapper .session-inner-wrapper .session-body,
		.todo-wrapper .task-box,
		figure.img-wrapper::after,
		.plan-info,
		.btn-light,
		.rct-mail-wrapper header,
		.todo-wrapper header {
			background: $dark-bg-container;
		}

		.rct-mail-wrapper .list-wrap ul .list-item:hover,
		.todo-wrapper .list-wrap ul .list-item:hover,
		.chat-sidebar .chat-list ul .user-list-item:hover,
		.task-detail-top {
			background: $dark-secondary-bg !important;
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6,
		p,
		a:not([class*="text-danger"]):not([class*="text-primary"]),
		// span:not([class*="text-danger"]):not([class*="text-primary"]),
		table,
		th,
		td,
		select,
		input,
		.menu-icon,
		.side-arrow,
		.top-news-wrap .slider-btn-wrap .slick-arrow:before,
		input::placeholder,
		textarea::placeholder,
		.form-control,
		.header-title,
		.humburger,
		.header-icon,
		.notification-icon a button,
		.cart-icon a button,
		.text-muted,
		.page-title h2,
		.active.breadcrumb-item,
		.text-dark,
		textarea,
		.total-earn-chart .default-btn-group button,
		.close:not(:disabled):not(.disabled),
		.sidebar-user .sidebar-user-block .rct-dropdown .dropdown-menu ul li:not(.user-profile-top) a span:not(.badge),
		.ais-Panel-header,
		.dark-theme-text-color {
			color: $dark-theme-text-color !important;
		}

		.dropdown-item,
		.ReactTable .rt-thead .rt-th,
		.ReactTable .rt-thead .rt-td,
		.ReactTable .rt-tbody .rt-th,
		.ReactTable .rt-tbody .rt-td,
		.sidebar-user .user-profile>.MuiAvatar-root,
		span:not([class*="MuiIconButton-label"]) {
			color: $dark-theme-text-color;
		}

		// .rct-block,
		.pricing-wrapper,
		.about-wrapper,
		.panel-wrapper,
		.list-wrapper,
		.menu-paper-wrapper,
		.popover-wrapper,
		.date-time-wrapper,
		.stepper-wrapper,
		.tabs-wrapper,
		.textfields-wrapper,
		.select-wrapper,
		.re-chart-wrapper,
		.icons-wrapper,
		.data-table-wrapper,
		.redux-table-wrapper,
		.divider-wrapper,
		.drawer-wrapper,
		.feedback-wrapper,
		.userProfile-wrapper,
		.avatar-wrapper,
		.calendar-wrapper,
		.deal-comments {
			*:not([style*="color:"]):not(.text-danger) {
				color: $dark-theme-text-color !important;
			}
		}

		.StepperButtonGroupComponent .steps-wrapper ul.steps li:not(.active)>div,
		.StepperButtonGroupComponent .steps-wrapper ul.steps li:not(.active)>div:before,
		.rct-block,
		.social-card,
		.card,
		.panel,
		.rct-tabs header,
		.table th,
		.table td,
		.rct-block-footer,
		.pricing-box,
		.chat-bubble.even,
		.btn-light i {
			background-color: $dark-bg;
			color: $dark-theme-text-color;
			border-color: $dark-bg-border-color;
		}

		.StepperButtonGroupComponent .steps-wrapper ul.steps li.past:hover>div,
		.StepperButtonGroupComponent .steps-wrapper ul.steps li.past:hover>div:before {
			background-color: $dark-bg-hover-bg;
		}

		.StepperButtonGroupComponent .steps-wrapper ul.steps,
		.StepperButtonGroupComponent .steps-wrapper ul.steps li>div:before {
			border-color: $darker;
		}

		.quicklink-wrapper .header-icon,
		.search-icon .search-wrapper .search-input-lg,
		.dark-theme-border-white {
			border-color: $white !important;
		}

		.dark-theme-border-dark {
			border-color: $dark !important;
		}

		.dark-theme-border-darker {
			border-color: $darker !important;
		}

		.dark-theme-border-danger {
			border-color: $danger !important;
		}

		.dark-theme-border-success {
			border-color: $success !important;
		}

		.dark-theme-border-primary {
			border-color: $primary !important;
		}

		.dark-theme-bg-white {
			background-color: $white !important;
		}

		.dark-theme-bg-dark {
			background-color: $dark !important;
		}

		.dark-theme-bg-danger {
			background-color: $danger !important;
		}

		.dark-theme-bg-success {
			background-color: $success !important;
		}

		.dark-theme-bg-primary {
			background-color: $primary !important;
		}

		.dark-theme-bg-light {
			background: $dark !important;
		}

		.dark-theme-bg-white {
			background: $white !important;
		}

		.modal-header,
		.modal-footer,
		.report-status,
		// .rct-block *:not([style*="border-color:"]):not([class*="border-"]),
		.report-status *:not([style*="border-color:"]):not([class*="border-"]),
		.calendar-wrapper *:not([style*="border-color:"]):not([class*="border-"]),
		.rct-mail-wrapper *:not([style*="border-color:"]):not([class*="border-"]),
		.todo-wrapper *:not([style*="border-color:"]):not([class*="border-"]),
		.chat-wrapper *:not([style*="border-color:"]):not([class*="border-"]),
		.dropdown *:not([style*="border-color:"]):not([class*="border-"]),
		.chat-head,
		.dark-bg-border-color {
			border-color: $dark-bg-border-color !important;
		}

		input:not(.input-search),
		textarea {
			background-color: transparent !important;
		}

		.app-boxed .agency-menu {
			box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.5);

			.nav-item-active,
			.sub-menu,
			.sub-menu-child,
			.sub-menu-sub-child {
				&:after {
					border-color: transparent transparent $dark-bg $dark-bg !important;
					box-shadow: -3px 3.5px 7px rgba(5, 0, 0, 0.25) !important;
				}
			}
		}

		.language-icon,
		.upgrade-btn {
			background-color: $primary !important;
		}
	}

	.dark-mode {
		.sidebar-user .sidebar-user-block .rct-dropdown .dropdown-menu {
			box-shadow: 0 0 0;

			>ul>li {
				margin: 0;
			}
		}

		.cke_dialog_body {
			background-color: $darker;

			.cke_dialog_title {
				color: $dark-theme-text-color;
				background-color: $dark-bg;
			}

			a.cke_dialog_ui_button {
				border-color: $primary;
				background: $primary;
				text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
			}

			a.cke_dialog_ui_button_ok {
				color: #fff;
				background: $primary !important;
				border: 1px solid $primary;
			}

			a.cke_dialog_ui_button_cancel {
				color: #fff;
				background: $danger !important;
				border: 1px solid $danger;
			}
		}

		.cke_dialog_tab,
		.cke_dialog_tab:hover,
		.cke_dialog_contents {
			background-color: $dark-bg;

			*:not([style*="color:"]) {
				background-color: transparent;
				color: $dark-theme-text-color !important;
			}
		}

		#react-select-single,
		#react-select-chip {

			.css-yk16xz-control,
			.css-1pahdxg-control {
				border-color: transparent !important;
				background-color: transparent !important;
				box-shadow: none !important;
			}

			.css-26l3qy-menu {
				>div {
					background-color: $dark-bg ;
				}

				>div>div {
					cursor: pointer;
				}

				>div>div:hover,
				>div>div:focus {
					background: rgba(0, 0, 0, 0.2) !important;
				}
			}
		}

		.IntegrationAutosuggest-suggestionsContainerOpen-472 {
			background-color: transparent;
		}
	}

	/* qiplus */
	.dark-mode {

		.conversion-stats-wrapper:before,
		.conversion-stats-wrapper:after,
		.conversion-stats-wrapper .conversion-stats-aside:before,
		.conversion-stats-wrapper .conversion-stats-aside:after,
		.labeled-divider .MuiFormControlLabel-root,
		.MuiPaper-root,
		.labeled-divider .label,
		.user-profile-widget .MuiTabs-root,
		.calendar-modal .MuiTabs-root,
		.calendar-wrapper th.fc-day-header,
		.user-management .form-body,
		.form-fields-container #styles-wrapper,
		.MailSchedule-Component,
		.deal-comments,
		.deal-comments>.label,
		.desk-notifications .notification-list li,
		.desk-notifications .notification-list li:hover,
		.automation-canvas .float-btn .MuiIconButton-label,
		.rct-mail-wrapper .compose-email-container .input-group,
		.rct-mail-wrapper .compose-email-container .input-group .MuiTextField-root.bg-white,
		.rct-mail-wrapper .compose-email-container .input-group .MuiInputBase-input .MuiChip-root,
		.rct-sidebar .rct-sidebar-content.sidebar-overlay-dark:before {
			background-color: $dark-bg !important;
		}

		.dark-theme-bg-dark,
		.rct-mail-wrapper .alert-light,
		.email-detail-page-wrapper .mail-detail,
		.rct-sidebar .rct-sidebar-content.sidebar-overlay-dark:before {
			background: $dark-bg !important;
		}

		.dark-theme-bg-dark-medium,
		.table thead th,
		.MuiDialogTitle-root,
		.FlexGridList .FlexGridButton,
		.duallistbox-choices .duallistbox-selected,
		.user-profile-head,
		.desk-notifications .notification-list,
		.dropdown-menu,
		.rct-block .badge-gray,
		.rct-block .qi-icon-button:not([class*="bg-"]),
		.rct-block .rct-block-title,
		.rct-block .rct-block-tags,
		.rct-block .rt-table .rt-thead,
		.MuiAutocomplete-groupLabel,
		.sidebar-user-block .rct-dropdown .dropdown-menu,
		.sidebar-user-block .rct-dropdown .dropdown-menu ul.list-unstyled,
		.automation-canvas.triggers-canvas .stage-wrapper,
		.adv-search-bar-wrap .bg-dark,
		.VideosModuleComponent .rct-block-content,
		.ImagesModuleComponent .rct-block-content {
			background: $darker !important;
		}

		.badge.badge-light,
		.badge.badge-gray,
		.rct-block .alert:not([class*="bg-"]),
		.rct-block .alert-dark,
		.alert.alert-light,
		.alert.alert-info,
		.alert.alert-gray,
		.alert.alert-primary,
		.alert.alert-linkedin,
		.alert.alert-warning,
		.MuiPickersToolbarText-toolbarBtnSelected,
		.chat-sidebar .sidebar-filters-wrap .filters>div.item-active,
		.mail-sidebar-wrap .sidebar-filters-wrap .filters>div.item-active,
		.todo-sidebar-wrap .sidebar-filters-wrap .filters>div.item-active,
		.sidebar-user-block .rct-dropdown .dropdown-menu ul li:not(.user-profile-top) a,
		.mail-sidebar-wrap .dropdown-menu,
		.todo-sidebar-wrap .dropdown-menu,
		.chat-sidebar .dropdown-menu,
		.fc-unthemed td.fc-today,
		.calendar-wrapper .fc-unthemed td.fc-today,
		.calendar-wrapper button.fc-button-primary,
		.calendar-wrapper button.fc-button-primary:not(:disabled),
		.calendar-wrapper button.fc-button-primary:not(:disabled).fc-button-active {
			color: $white !important;
			background: $dark-bg-button !important;
		}

		.form-fields-container .qi-icon-button .btn-contents .btn-label,
		.form-fields-container .qi-icon-button .btn-contents input,
		.form-fields-container .qi-icon-button .btn-contents small {
			color: #464e67 !important;
		}

		.mail-sidebar-wrap .dropdown-menu,
		.todo-sidebar-wrap .dropdown-menu,
		.chat-sidebar .dropdown-menu {
			box-shadow: 1px 2px 10px rgba(0, 0, 0, 0.3);
		}

		// .rct-block .MuiButton-outlinedPrimary:not(.Mui-disabled) {
		// 	background: $primary !important;
		// }
		// .rct-block .MuiButton-outlinedSecondary:not(.Mui-disabled) {
		// 	background: $warning !important;
		// }
		.rct-block .cke_top,
		.cke_top {
			background: $darker;

			.cke_button_icon {
				filter: saturate(0) contrast(0);
			}

			.cke_button_off:hover,
			.cke_button_off:focus,
			.cke_button_off:active,
			.cke_button_on {
				background: $dark-bg-button !important;
			}
		}

		.chrome-picker input {
			color: #333 !important;
		}

		.rct-block .cke_contents,
		.cke_contents {

			/* Código Fonte */
			textarea:not(.this-is-only-to-overwrite-important) {
				color: $black !important;
			}
		}

		.recharts-wrapper {
			.recharts-tooltip-wrapper {
				background: #141517;
				text-align: left;
				padding: 10px;
				color: #fff;
				line-height: 1.1;
			}

			path.recharts-rectangle.recharts-tooltip-cursor {
				fill: rgba(255, 255, 255, 0.05);
			}
		}

		.desk-notifications .notification-top {
			color: $white !important;
			background: $dark-bg-body !important;
		}

		.automation-canvas .stage-col+.stage-col:before {
			background-color: transparent !important;
		}

		.form-canvas {
			label:not([style*="color:"]) {
				color: #464D69 !important;
			}

			input,
			textarea {
				background-color: inherit !important;
			}
		}

		.projects-wrapper .alert-danger,
		.MuiDialog-root .alert-danger,
		.rct-block .alert-danger {
			background: #851d1e !important;
		}

		.projects-wrapper .alert-warning,
		.MuiDialog-root .alert-warning,
		.rct-block .alert-warning {
			background: $warning !important;
		}

		.projects-wrapper .alert-success,
		.MuiDialog-root .alert-success,
		.rct-block .alert-success {
			color: #fff;
			background: #006c0a;
			border-color: #006c0a;
		}

		.MuiSwitch-colorPrimary.Mui-checked,
		.MuiIconButton-colorPrimary.Mui-checked .MuiIconButton-label,
		.MuiButton-textPrimary .MuiButton-label,
		.MuiButton-textPrimary .MuiButton-label span:not([class*="dark-theme-text-"]),
		.MuiButton-textPrimary .MuiButton-label i {
			color: #639aff !important;
		}

		.MuiButton-containedPrimary:not([class*="bg-"]) {
			color: #fff !important;
			background-color: #5D92F4 !important;
		}

		.MuiStepper-root {
			background-color: $darker;

			.MuiStepConnector-line {
				border-color: $white !important;
			}

			.MuiStep-root {
				.MuiStepIcon-text {
					fill: $dark !important;
				}
			}
		}

		.FlexGridList {

			.FlexGridButton>div,
			.FlexGridButton>div>div {
				color: $dark !important;
				background-color: $white !important;
			}
		}

		.tasklist-progress {
			position: relative;

			&:before {
				content: '';
				z-index: 0;
				position: absolute;
				top: -3px;
				left: -15px;
				right: -15px;
				bottom: -3px;
				background: #232b36;
			}

			>* {
				position: relative;
				z-index: 1;
			}
		}

		.page-link {
			background-color: $dark-bg;
			border: 1px solid $dark-bg-body;
		}

		.page-item.disabled .page-link {
			color: $dark-bg;
		}

		.chat-sidebar {
			border: 0 !important;
		}

		.chat-message-bubble {
			background-color: rgba($color: #fff, $alpha: 0.05) !important;
		}

		.text-qiplus,
		.rct-block .text-qiplus {
			color: $qiplus-color !important;
		}

		.border-qiplus,
		.rct-block .border-qiplus {
			border-color: $qiplus-color !important;
		}

		.bg-qiplus,
		.rct-block .bg-qiplus {
			background-color: $qiplus-color !important;
		}

		.bg-gradient-qiplus,
		.rct-block .bg-gradient-qiplus {
			background: $gradient-qiplus !important;
		}

		.text-transparent,
		.rct-block .text-transparent {
			color: transparent !important;
		}

		.border-transparent,
		.rct-block .border-transparent {
			border-color: transparent !important;
		}

		.bg-transparent,
		.rct-block .bg-transparent {
			background-color: transparent !important;
		}

		.MuiSwitch-colorSecondary.Mui-checked+.MuiSwitch-track {
			background-color: $qiplus-color !important;
		}

		.MuiSwitch-colorPrimary.Mui-checked+.MuiSwitch-track {
			background-color: $qiplus-color !important;
		}

		.MuiSwitch-colorSecondary.Mui-checked {
			color: $qiplus-color !important;
		}

		.MuiSwitch-thumb {
			color: white !important;
		}

		.MuiBox-root {
			background-color: transparent !important;
		}

		.MuiAlert-standardError {
			background-color: #FF3739 !important;
			color: white !important;

			.MuiAlert-icon {
				color: white !important;
			}
		}

		.MuiAlert-standardSuccess {
			background-color: #00D014 !important;
			color: white !important;

			.MuiAlert-icon {
				color: white !important;
			}
		}

		.MuiAlert-standardWarning {
			background-color: #d4990e !important;
			color: white !important;

			.MuiAlert-icon {
				color: white !important;
			}
		}

		.MuiAlert-standardInfo {
			background-color: #0fa7ff !important;
			color: white !important;

			.MuiAlert-icon {
				color: white !important;
			}
		}
	}

	/* end qiplus */
}
