import { Button, Dialog, DialogActions } from '@material-ui/core';
import { Loading } from 'Components/Widgets/components/Loading';
import { langMessages } from 'Lang/index';
import React from 'react';
import { ShotxDialogForm } from './ShotxDialogForm';
import { ShotxDialogTitle } from './ShotxDialogTitle';

export function ShotxDialog(props) {
  const {
    open,
    title,
    content,
    isLoading,
    onBack,
    onNext,
    onClose,
    disableNext,
    disablePrevious,
    nextText,
    backText,
    width = '60vw',
    height = '60vh',
    buttonActiveColor = 'bg-qiplus text-white',
  } = props

  return (
    <Dialog
      onClose={onClose}
      aria-labelledby="alert-dialog-title"
      open={open}
      fullWidth={true}
      maxWidth={'md'}
      scroll="paper"
      PaperProps={{
        style: {
          minHeight: height,
          // maxHeight: height,
          minWidth: width,
          // maxWidth: width,
        },
      }}
    >
      <ShotxDialogTitle onClose={onClose} title={title} {...props} />
      <Loading loading={isLoading} label={langMessages['texts.loading']}>
        {<ShotxDialogForm {...content} />}
      </Loading>
      <DialogActions>
        {onBack && <Button variant="contained" disabled={disablePrevious || isLoading} onClick={onBack} className={disablePrevious ? 'secondary' : buttonActiveColor} >
          {backText || langMessages['button.back']}
        </Button>}
        <Button variant="contained" onClick={onNext} disabled={disableNext || isLoading} className={disableNext ? 'secondary' : buttonActiveColor}>
          {nextText || langMessages['button.next']}
        </Button>
      </DialogActions>
    </Dialog>
  )
}
