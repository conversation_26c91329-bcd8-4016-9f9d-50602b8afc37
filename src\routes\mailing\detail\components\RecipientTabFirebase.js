/**
 * RecipientTabFirebase Component
 * Componente para a aba "recipient" que usa DualListBox para seleção de leads e segmentações
 */
import { But<PERSON>, Divider, LinearProgress } from '@material-ui/core';
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard';
import SimpleDualListBox from 'Components/UIComponents/duallistbox/SimpleDualListBox';
import { FirestoreRef } from 'FirebaseRef';
import { FirebaseRepository } from 'FirebaseRef/repository';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { LEADS_COLLECTION_NAME, SEGMENTATIONS_COLLECTION_NAME } from '../../../../constants/AppCollections';
import { langMessages } from '../../../../lang';

const RecipientTabFirebase = ({
  title,
  settings,
  accountId,
  FilterNavBar,
  onUpdateSettings,
}) => {
  // Usar useRef para evitar recriação do repository a cada renderização
  const repositoryRef = React.useRef(null);
  if (!repositoryRef.current) {
    repositoryRef.current = new FirebaseRepository();
  }

  // Estado para armazenar os leads selecionados
  const [selectedContacts, setSelectedContacts] = useState(settings?.contacts || []);
  // Estado para armazenar as segmentações selecionadas
  const [selectedSegmentations, setSelectedSegmentations] = useState(settings?.segmentations || []);
  // Estado para controlar a exibição do seletor de leads
  const [showLeadSelector, setShowLeadSelector] = useState(false);
  // Estado para controlar a exibição do seletor de segmentações
  const [showSegmentationSelector, setShowSegmentationSelector] = useState(false);
  // Estados para o seletor de leads
  const [allLeads, setAllLeads] = useState([]);
  const [loadingLeads, setLoadingLeads] = useState(true);
  // Estados para paginação
  const [lastVisibleDoc, setLastVisibleDoc] = useState(null);
  const [hasMoreLeads, setHasMoreLeads] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  // Estado para controlar se deve mostrar apenas leads com email
  const [showOnlyWithEmail, setShowOnlyWithEmail] = useState(true);
  // Estado para armazenar as segmentações carregadas diretamente do Firebase
  const [allSegmentations, setAllSegmentations] = useState([]);
  const [loadingSegmentations, setLoadingSegmentations] = useState(true);

  // Função para atualizar os leads selecionados (apenas no estado local)
  const handleContactsChange = (contacts) => {
    // Garantir que todos os IDs sejam strings
    const contactsAsStrings = Array.isArray(contacts)
      ? contacts.map(id => id ? id.toString() : '')
      : [];

    // Remover valores vazios
    const validContacts = contactsAsStrings.filter(id => id);

    // Apenas atualiza o estado local, não envia para o Redux ainda
    setSelectedContacts(validContacts);
  };

  // Função para atualizar as segmentações selecionadas (apenas no estado local)
  const handleSegmentationsChange = (segmentations) => {
    // Garantir que todos os IDs sejam strings
    const segmentationsAsStrings = Array.isArray(segmentations)
      ? segmentations.map(id => id ? id.toString() : '')
      : [];

    // Remover valores vazios e duplicatas
    const validSegmentations = [...new Set(segmentationsAsStrings.filter(id => id))];

    // Apenas atualiza o estado local, não envia para o Redux ainda
    setSelectedSegmentations(validSegmentations);
  };

  // Efeito para carregar os leads quando o componente é montado ou quando as configurações mudam
  useEffect(() => {
    if (settings) {
      // Atualizar contatos selecionados
      const contactsFromSettings = settings.contacts || [];

      // Garantir que todos os IDs sejam strings
      const contactsAsStrings = contactsFromSettings.map(id => id ? id.toString() : '').filter(id => id);

      // Apenas atualiza o estado local com os valores do Redux
      setSelectedContacts(contactsAsStrings);

      // Remover possíveis duplicatas nas segmentações das configurações
      const segmentationsFromSettings = settings.segmentations || [];

      // Garantir que todos os IDs sejam strings
      const segmentationsAsStrings = segmentationsFromSettings.map(id => id ? id.toString() : '').filter(id => id);

      // Remover duplicatas
      const uniqueSegmentations = [...new Set(segmentationsAsStrings)];

      // Apenas atualiza o estado local com os valores do Redux
      setSelectedSegmentations(uniqueSegmentations);

      // Se houver diferença nas segmentações (duplicatas), atualizar o Redux apenas na primeira carga
      // Isso é necessário para corrigir possíveis duplicatas que já existam no Redux
      if (segmentationsFromSettings.length !== uniqueSegmentations.length && onUpdateSettings) {
        onUpdateSettings({
          ...settings,
          segmentations: uniqueSegmentations,
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [settings]);

  // Função para processar os documentos do Firebase e convertê-los para o formato do DualListBox
  const processLeadDocs = React.useCallback((docs) => {
    // Primeiro mapeamos todos os documentos
    const mappedLeads = docs.map(doc => {
      const data = doc.data();
      // Garantir que o lead tenha um título válido para evitar o fallback 'Q'
      const displayName = data.displayName || `${data.firstName || ''} ${data.lastName || ''}`.trim() || 'Sem nome';

      // Garantir que o ID seja uma string
      const leadId = data.ID ? data.ID.toString() : '';

      return {
        id: leadId, // Importante: o DualListBox espera 'id' como string, não 'value'
        title: displayName,
        subtitle: data.email, // Adicionamos o email como subtitle
        firstName: data.firstName,
        lastName: data.lastName,
        email: data.email,
        status: data.status,
        img: data.avatar, // Importante: o DualListBox espera 'img', não 'avatar'
        avatarColor: 'primary',
        // Garantir que o avatar tenha uma letra inicial válida
        icon: displayName ? null : 'icon-user', // Usar ícone de usuário se não tiver nome
        // Armazenar o documento original para paginação
        _doc: doc
      };
    });

    // Filtrar os leads para excluir aqueles com status 'trash'
    const filteredLeads = mappedLeads.filter(lead => {
      // Se o lead não tiver status ou o status não for 'trash', incluir
      return !lead.status || lead.status !== 'trash';
    });

    // Não precisamos mais filtrar aqui, pois já estamos filtrando no Firebase
    return filteredLeads;
  }, [showOnlyWithEmail]);

  // Função para carregar a primeira página de leads
  const loadLeads = React.useCallback(() => {
    setLoadingLeads(true);
    setHasMoreLeads(true);
    setLastVisibleDoc(null);

    const ref = repositoryRef.current.firebase || FirestoreRef;
    let query = ref.collection(LEADS_COLLECTION_NAME);

    // Filtrar por accountId
    query = query.where('accountId', '==', `${accountId}`);

    // Ordenar primeiro por email (obrigatório quando usamos desigualdade no where)
    // e depois por nome
    if (showOnlyWithEmail) {
      // Quando usamos desigualdade, precisamos ordenar primeiro pelo mesmo campo
      query = query.orderBy('email', 'asc');
      // Depois podemos ordenar por outros campos
      query = query.orderBy('firstName', 'asc');
      // Agora podemos filtrar por email não vazio
      // Usamos uma abordagem diferente: filtrar por email que começa com qualquer caractere
      query = query.where('email', '>=', ' '); // Qualquer string não vazia é maior que espaço
    } else {
      // Se não estamos filtrando por email, podemos ordenar diretamente por nome
      query = query.orderBy('firstName', 'asc');
    }

    // Aumentar o limite para 50 leads por página para garantir que mais leads sejam carregados
    const pageSize = 50;
    query = query.limit(pageSize);

    query.get().then(snapshot => {
      if (!snapshot.empty) {
        // Processar os documentos
        const leads = processLeadDocs(snapshot.docs);

        // Armazenar o último documento para paginação
        if (snapshot.docs.length > 0) {
          const lastDoc = snapshot.docs[snapshot.docs.length - 1];
          setLastVisibleDoc(lastDoc);
        } else {
          setLastVisibleDoc(null);
          setHasMoreLeads(false);
        }

        // Verificar se há mais leads para carregar
        setHasMoreLeads(leads.length === pageSize);

        setAllLeads(leads);
      } else {
        setAllLeads([]);
        setHasMoreLeads(false);
      }
      setLoadingLeads(false);
    }).catch(() => {
      setLoadingLeads(false);
      setHasMoreLeads(false);
    });
  }, [accountId, processLeadDocs]);

  // Função para carregar mais leads (próxima página)
  const loadMoreLeads = React.useCallback(() => {
    if (!lastVisibleDoc || isLoadingMore || !hasMoreLeads) {
      return;
    }

    setIsLoadingMore(true);

    const ref = repositoryRef.current.firebase || FirestoreRef;
    let query = ref.collection(LEADS_COLLECTION_NAME);

    // Filtrar por accountId
    query = query.where('accountId', '==', `${accountId}`);

    // Ordenar primeiro por email (obrigatório quando usamos desigualdade no where)
    // e depois por nome
    if (showOnlyWithEmail) {
      // Quando usamos desigualdade, precisamos ordenar primeiro pelo mesmo campo
      query = query.orderBy('email', 'asc');
      // Depois podemos ordenar por outros campos
      query = query.orderBy('firstName', 'asc');
      // Agora podemos filtrar por email não vazio
      // Usamos uma abordagem diferente: filtrar por email que começa com qualquer caractere
      query = query.where('email', '>=', ' '); // Qualquer string não vazia é maior que espaço
    } else {
      // Se não estamos filtrando por email, podemos ordenar diretamente por nome
      query = query.orderBy('firstName', 'asc');
    }

    // Começar depois do último documento carregado
    // Importante: passamos o documento completo, não apenas o valor do campo
    if (lastVisibleDoc) {
      // Usar o documento completo para startAfter
      query = query.startAfter(lastVisibleDoc);
    } else {
      setIsLoadingMore(false);
      return;
    }

    // Aumentar o limite para 50 leads por página
    const pageSize = 50;
    query = query.limit(pageSize);

    query.get().then(snapshot => {
      if (!snapshot.empty) {
        // Processar os documentos
        const newLeads = processLeadDocs(snapshot.docs);

        // Armazenar o último documento para paginação
        if (snapshot.docs.length > 0) {
          const lastDoc = snapshot.docs[snapshot.docs.length - 1];
          setLastVisibleDoc(lastDoc);
        } else {
          setHasMoreLeads(false);
        }

        // Verificar se há mais leads para carregar
        setHasMoreLeads(newLeads.length === pageSize);

        // Adicionar os novos leads aos já carregados
        setAllLeads(prevLeads => [...prevLeads, ...newLeads]);
      } else {
        setHasMoreLeads(false);
      }
      setIsLoadingMore(false);
    }).catch(() => {
      setIsLoadingMore(false);
    });
  }, [accountId, lastVisibleDoc, isLoadingMore, hasMoreLeads, processLeadDocs]);

  // Carregar leads quando o componente for montado
  useEffect(() => {
    loadLeads();
  }, [loadLeads]);

  // Carregar leads quando o seletor for aberto
  useEffect(() => {
    if (showLeadSelector && allLeads.length === 0) {
      loadLeads();
    }
  }, [showLeadSelector, allLeads.length, loadLeads]);

  // Função para carregar segmentações diretamente do Firebase
  const loadSegmentations = React.useCallback(() => {
    setLoadingSegmentations(true);

    const ref = repositoryRef.current.firebase || FirestoreRef;
    let query = ref.collection(SEGMENTATIONS_COLLECTION_NAME);

    // Filtrar por accountId
    query = query.where('accountId', '==', `${accountId}`);

    // Ordenar por título
    query = query.orderBy('title', 'asc');

    query.get().then(snapshot => {
      if (!snapshot.empty) {
        // Processar os documentos
        const segmentations = snapshot.docs.map(doc => {
          const data = doc.data();
          // Garantir que o ID seja uma string
          const segId = data.ID ? data.ID.toString() : '';

          return {
            id: segId, // Importante: o DualListBox espera 'id' como string
            title: data.title,
            avatarColor: 'primary'
          };
        });

        setAllSegmentations(segmentations);
      } else {
        setAllSegmentations([]);
      }
      setLoadingSegmentations(false);
    }).catch(() => {
      setLoadingSegmentations(false);
    });
  }, [accountId]);

  // Carregar segmentações quando o seletor for aberto
  useEffect(() => {
    if (showSegmentationSelector) {
      loadSegmentations();
    }
  }, [showSegmentationSelector, loadSegmentations]);



  // Renderização do componente principal
  return (
    <div>
      <RctCollapsibleCard
        fullBlock
        heading={
          <div className="d-flex justify-content-between align-items-center">
            <div className="flex-1">
              <h3>{title || langMessages['emails.recipients']}</h3>
            </div>
            {!!FilterNavBar && <div className="flex-1">{FilterNavBar}</div>}
          </div>
        }
      >
        <div className="p-20">
          {/* Seletor de Contatos */}
          <div className="mb-20">
            <h4 className="mb-10">{langMessages['menu.contacts']} ({selectedContacts?.length || 0})</h4>
            {loadingLeads ? (
              <LinearProgress />
            ) : (
              <div>
                {showLeadSelector ? (
                  <div>
                    <div>
                      <SimpleDualListBox
                        autoHeightMin={300}
                        autoHeightMax={500}
                        filterInput
                        choices={allLeads}
                        values={selectedContacts || []}
                        onChange={ids => {
                          handleContactsChange(ids);
                        }}
                        renderItem={item => (
                          <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                            <div style={{ flex: 1, minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                              <div style={{ fontWeight: 500, marginBottom: '2px' }}>{item.title}</div>
                              {item.subtitle && <div className="text-muted" style={{ fontSize: '0.85em', paddingLeft: '2px' }}>{item.subtitle}</div>}
                            </div>
                          </div>
                        )}
                        isLoading={loadingLeads}
                        onLoadMore={loadMoreLeads}
                        hasMore={hasMoreLeads}
                        isLoadingMore={isLoadingMore}
                      />

                      {/* Botões de ação */}
                      {/* <div className="mt-10 d-flex justify-content-between">

                        <Button
                          variant="outlined"
                          color={showOnlyWithEmail ? "secondary" : "default"}
                          onClick={() => {
                            setShowOnlyWithEmail(!showOnlyWithEmail);
                            // Recarregar os leads com a nova configuração
                            loadLeads();
                          }}
                        >
                          {showOnlyWithEmail ? 'Mostrar todos os leads' : 'Mostrar apenas leads com email'}
                        </Button>
                      </div> */}
                    </div>
                    <div className="text-right mt-10">
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => {
                          // Salvar os contatos selecionados no Redux quando clicar em Concluir
                          if (onUpdateSettings) {
                            onUpdateSettings({
                              ...settings,
                              contacts: selectedContacts,
                            });
                          }
                          setShowLeadSelector(false);
                        }}
                      >
                        {langMessages['texts.done'] || 'Concluir'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <p className="text-muted">
                      {selectedContacts?.length > 0
                        ? `${selectedContacts.length} contatos selecionados`
                        : 'Nenhum contato selecionado.'}
                    </p>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => setShowLeadSelector(true)}
                    >
                      {selectedContacts?.length > 0 ? 'Alterar seleção' : 'Selecionar contatos'}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>

          <Divider className="my-20" />

          {/* Seletor de Segmentações */}
          <div className="mb-10">
            <h4 className="mb-10">{langMessages[`modules.${SEGMENTATIONS_COLLECTION_NAME}`]} ({selectedSegmentations?.length || 0})</h4>
            {showSegmentationSelector ? (
              <div>
                <SimpleDualListBox
                  autoHeightMin={200}
                  filterInput
                  choices={allSegmentations}
                  isLoading={loadingSegmentations}
                  renderItem={item => (
                    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <div style={{ flex: 1, minWidth: 0, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                        <div style={{ fontWeight: 500, marginBottom: '2px' }}>{item.title}</div>
                      </div>
                    </div>
                  )}
                  values={selectedSegmentations || []}
                  onChange={ids => {
                    // Garantir que não há duplicatas nos IDs selecionados
                    const uniqueIds = Array.isArray(ids) ? [...new Set(ids)] : [];
                    handleSegmentationsChange(uniqueIds);
                  }}
                />
                <div className="text-right mt-10">
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={() => {
                      // Salvar as segmentações selecionadas no Redux quando clicar em Concluir
                      if (onUpdateSettings) {
                        onUpdateSettings({
                          ...settings,
                          segmentations: selectedSegmentations,
                        });
                      }
                      setShowSegmentationSelector(false);
                    }}
                  >
                    {langMessages['texts.done'] || 'Concluir'}
                  </Button>
                </div>
              </div>
            ) : (
              <div>
                <p className="text-muted">
                  {selectedSegmentations?.length > 0
                    ? `${selectedSegmentations.length} segmentações selecionadas`
                    : 'Nenhuma segmentação selecionada.'}
                </p>
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={() => setShowSegmentationSelector(true)}
                >
                  {selectedSegmentations?.length > 0 ? 'Alterar seleção' : 'Selecionar segmentações'}
                </Button>
              </div>
            )}
          </div>
        </div>
      </RctCollapsibleCard>
    </div>
  );
};

const mapStateToProps = ({ authReducer, postsReducer }) => {
  const { account } = authReducer;
  const { posts } = postsReducer;

  return {
    account,
    accountId: account.ID,
    taxonomies: posts.taxonomies || { tags: { leads: [] } },
  };
};

// Exporta o componente conectado ao Redux
export { RecipientTabFirebase };

export default connect(mapStateToProps)(RecipientTabFirebase);
