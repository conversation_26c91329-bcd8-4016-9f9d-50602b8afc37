// lang strings
import { langMessages } from '../../../../lang'

const Fields = {
  tabs: [
    {
      tab: 'content',
      label: 'Conteúdo',
    },
    {
      tab: 'settings',
      label: 'Configurações',
    },
  ],

  fields: [
    {
      block: 'content',
      key: '',
      label: '<PERSON><PERSON>tu<PERSON>',
      name: 'title',
      type: 'text',
      instructions: 'Título identificador do aviso',
      required: 1,
      conditional_logic: 0,
      wrapper: {
        width: '100',
        class: 'mb-20',
        id: '',
      },
      default_value: '',
      placeholder: 'Digite o título do aviso',
    },
    {
      block: 'content',
      key: '',
      label: 'Conteúdo para Email',
      name: 'content_mail',
      type: 'textarea',
      instructions: 'Conteúdo específico para envio por email (pode ser mais detalhado)',
      required: 1,
      conditional_logic: 0,
      wrapper: {
        width: '100',
        class: 'mb-20',
        id: '',
      },
      default_value: '',
      placeholder: 'Digite o conteúdo que será enviado por email',
      rows: 6,
    },
    {
      block: 'content',
      key: '',
      label: 'Conteúdo para App',
      name: 'content_app',
      type: 'textarea',
      instructions: 'Conteúdo específico para exibição no aplicativo (versão mais concisa)',
      required: 1,
      conditional_logic: 0,
      wrapper: {
        width: '100',
        class: 'mb-20',
        id: '',
      },
      default_value: '',
      placeholder: 'Digite o conteúdo que será exibido no app',
      rows: 4,
    },
    {
      block: 'settings',
      key: '',
      label: 'Status Ativo',
      name: 'active',
      type: 'true_false',
      instructions: 'Define se o aviso está ativo e deve ser exibido',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: 'mb-20',
        id: '',
      },
      default_value: 1,
      message: 'Aviso ativo',
    },
    {
      block: 'settings',
      key: '',
      label: 'Autor',
      name: 'author',
      type: 'text',
      instructions: 'Responsável pela criação do aviso',
      required: 0,
      conditional_logic: 0,
      wrapper: {
        width: '50',
        class: 'mb-20',
        id: '',
      },
      default_value: '',
      placeholder: 'Nome do autor',
      readonly: 1,
    },
  ],
}

module.exports = Fields
