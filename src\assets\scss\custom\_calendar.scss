// -------------------------------------------
// Big Calendar 
// -------------------------------------------
.calendar-wrapper {
    .rbc-toolbar {
        .rbc-btn-group {
            button:not(.rbc-active):not(:hover):not(:active) {
                border-color: #ccc;
                color: #828181;
            }
            button:active:hover, 
            button.rbc-active:hover, 
            button:active:focus, 
            button.rbc-active:focus {
                background-color: #5D92F4;
                border-color: #5D92F4;
                color: #fff;
            }
            button {
                i[class^="ti-"] {
                    position: relative;
                    top: 2px;
                }    
            }
        }
    }
    .rbc-header {
        font-size: 13px;
        color: #464d69;
        font-weight: 300;
        padding: 2px 0;
    }
    .rbc-time-header {
        &.rbc-overflowing {
            margin-right: 0 !important;
        }
    }
    .rbc-time-view {
        overflow: hidden;
    }
    .rbc-time-content {
        // --------------------------------
        // FORCE SCROLLBAR AND LIMIT HEIGHT
        // --------------------------------
        max-height: calc(100vh - 280px);
        width: calc(100% + 17px);
        overflow-y: scroll !important;
        // END FORCE SCROLLBAR
        // --------------------------------
        > .rbc-time-gutter .rbc-label {
            font-size: 12px;
            color: #999;
        }        
    }
    .rbc-current-time-indicator {
        background: #c12477;
        left: 0 !important;
        width: 40px !important;
    }
    .rbc-month-row {
        .rbc-date-cell {
            text-align: center;
            font-size: 12px;
            padding: 2px 0;
            font-weight: 300;
        }
    }
    .rbc-today {
        background-color: #fbfbfb;
    }
    .rbc-event {
        padding: 1px 10px;
        transition:  .15s ease-out;
        transform:  scale(1);
        border-width: 1px;
        border-color: rgba(255, 255, 255, 0.23) !important;
        &.selected {
            box-shadow: 0 0 5px rgba(0,0,0,0.3);
            border-width: 3px;
            transform:  scale(1.1);
            z-index: 1;
        }
        &.rbc-event-allday {
            transform:  scale(1);
        }
        .rbc-event-content {
            font-size: 14px;
        }
    }
}

.calendar-modal {
    .modal-body {
        padding-top: 0;
    }
    .MuiTabs-root {
        margin: 0 -1rem;
        width: auto;
        background: #f9f9f9;
    }
    .MuiTab-root {
        min-width: auto;
        flex: 1;
    }
    .tab-notifications {
        .notification-type-before {
            .MuiOutlinedInput-inputSelect,
            input {
                padding: 17px 10px ;
            }        
        }
    }
}

.calendar-wrapper {
    .rbc-calendar {
        display: block;
    }
}

.rbc-month-row {
    min-height: 150px;
}

.rbc-event {
    background-color: $blue;
    padding: 10px;
    border-radius: 5px;
    border-color: $blue !important;
}

.rbc-toolbar button.rbc-active,
.rbc-toolbar button:hover {
    background-color: $blue;
    color: $white;
}

@media (max-width: 700px) and (min-width: 320px) {
    .rbc-toolbar {
        font-size: 12px;
        flex-direction: column;
        align-items: start;
        span {
            margin: 5px 0;
        }
    }
    .rbc-month-row {
        min-height: 70px;
    }
}

// embedded widgets
.deal-widget .rbc-toolbar-label,
.user-management .rbc-toolbar-label {
    text-align: right;
}
.deal-widget .rbc-toolbar-label + .rbc-btn-group,
.user-management .rbc-toolbar-label + .rbc-btn-group {
    display: none;
}
.deal-widget {
    .calendar-wrapper {
        .rbc-toolbar {
            .rbc-btn-group:first-of-type {
                transform: scale(0.8);
                transform-origin: left;
            }
            .rbc-toolbar-label {
                transform: scale(0.8);
                transform-origin: right;
            }
        }
        .rbc-time-content {
            max-height: calc(100vh - 320px);
        }
    }
}
// -------------------------------------------

// -------------------------------------------
// Full Calendar 
// -------------------------------------------
.calendar-wrapper {
    th.fc-day-header {
        background: #f9f9f9;
        font-weight: 300;
    }
    button.fc-button-primary {
        // color: #fff;
        // background-color: #464d69;
        // border-color: #2C3E50;
        // font-size: 13px;
        // font-weight: 200;
        color: #2C3E50;
        background-color: #f9f9f9;
        border-color: #ddd;
        font-size: 13px;        
    }
    button.fc-button-primary:not(:disabled):active, 
    button.fc-button-primary:not(:disabled).fc-button-active {
        color: #fff;
        background-color: #464d69;
        border-color: #464d69;
    }
    // main title
    .fc-toolbar {
        h2 {
           font-size: 1.25rem;
           margin: 0;
           font-weight: 300;
       }
       .fc-button-group {
            > .fc-button.inactive {
                opacity: 0.8;
                color: #999;
                box-shadow: inset 0 0 4px rgba(0,0,0,0.1);
            }
       }
    }
    a.fc-event {
        font-weight: 400;
        .fc-title {
            font-size: 13px;
            font-weight: 500;
        }
    }
    
    /* 
    a.fc-event.gcal-event {
        background: #ff2342;
        border-color: #ff2342;
    } 
    a.fc-event.qiplus-event {
        background: #ff2342;
        border-color: #ff2342;
    }
    */
}

body[data-section="leads"],
body[data-section="qiusers"],
body[data-section="deals"],
body[data-section="funnels"],
body[data-screen="pipeline"] {
    .fc-header-toolbar{
        .fc-center h2 {
            font-size: 0.85rem;
        }
        .fc-right {
            display: none;
        }
    } 
    .fc-timeGridDay-view {
        div.fc-day-grid {
            .fc-row.fc-week {
                td.fc-axis span {
                    display: none;
                }
            }
        }
    }
}
