/**
 * Product Stats Chart
 */
import React, { Component } from 'react'
import { Line } from 'react-chartjs-2'

// chart config
import ChartConfig from 'Constants/chart-config'

// options
const defaultOptions = {
  elements: {
    point: {
      radius: 3,
    },
  },
  spanGaps: true,
  showLines: true,
  // pointRadius: 50,
  // pointHoverBorderWidth: 1,
  legend: {
    display: false,
    labels: {
      fontColor: ChartConfig.legendFontColor,
    },
  },
  scales: {
    xAxes: [
      {
        gridLines: {
          offsetGridLines: true,
          display: false,
        },
        ticks: {
          fontColor: ChartConfig.axesColor,
        },
      },
    ],
    yAxes: [
      {
        gridLines: {
          drawBorder: true,
          zeroLineColor: ChartConfig.chartGridColor,
        },
        ticks: {
          fontColor: ChartConfig.axesColor,
          beginAtZero: true,
          // stepSize: 1,
          // stepSize: 1000,
          // padding: 40
        },
      },
    ],
  },
}

class ProductStatsChart extends Component {
  render() {
    const { labels, datasets, options } = this.props
    const data = canvas => {
      const ctx = canvas.getContext('2d')
      const _stroke = ctx.stroke
      ctx.stroke = function () {
        ctx.save()
        ctx.shadowColor = 'rgba(0,0,0,0.5)'
        ctx.shadowBlur = 5
        ctx.shadowOffsetX = 0
        ctx.shadowOffsetY = 2
        _stroke.apply(this, arguments)
        ctx.restore()
      }
      return {
        labels,
        datasets,
      }
    }
    return <Line data={data} options={{ ...defaultOptions, ...options }} height={95} />
  }
}

export default ProductStatsChart
