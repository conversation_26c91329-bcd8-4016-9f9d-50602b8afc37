/* eslint-disable no-undef */
import { Checkbox, FormControlLabel } from '@material-ui/core'
import { QIUSERS_COLLECTION_NAME } from 'Constants/AppCollections'
import { FirebaseRepository } from 'FirebaseRef/repository'
import { langMessages } from 'Lang/index'
import React from 'react'
import { Button, Modal, ModalBody, ModalFooter, ModalHeader } from 'reactstrap'

function ModalSimple(props) {
  const {
    hasOpen,
    showChecked = true,
    onToggle,
    title,
    primaryButton,
    secondaryButton,
    onClickPrimary,
    onClickSecondary,
    disablePrimary,
    ...args
  } = props
  const [checkedBox, setCheckedBox] = React.useState(false)

  const setToggle = () => {
    onToggle(!hasOpen)
  }

  const handleClickPrimary = () => {
    if (onClickPrimary) {
      onClickPrimary({ checkedBox: checkedBox })
    }
    setToggle()
  }

  const handleClickSecondary = () => {
    if (onClickSecondary) {
      onClickSecondary()
    }
    setToggle()
  }

  const handleToggleListItems = () => {
    const localUser = JSON.parse(localStorage.getItem('user'))

    const id = localUser?.ID
    const path = `${QIUSERS_COLLECTION_NAME}/${id}`

    const repository = new FirebaseRepository()

    try {
      repository.setDoc(path, { dontShowModalShotX: !checkedBox })
    } catch (error) {
      console.log('error set dontShowModalShotX', error)
    }
    setCheckedBox(!checkedBox)
  }

  return (
    <div>
      <Modal fade={true} isOpen={hasOpen} toggle={() => setToggle()} {...args}>
        <ModalHeader toggle={() => setToggle()}>{title || 'QiPlus Modal'}</ModalHeader>
        <ModalBody>
          <>{props.children}</>
        </ModalBody>

        <ModalFooter className="d-flex justify-content-between">
          <div>
            {showChecked && (
              <FormControlLabel
                control={<Checkbox color="primary" checked={checkedBox} onChange={() => handleToggleListItems()} disableRipple />}
                label={langMessages['modal.banner.checkedNoShowAgain']}
              />
            )}
          </div>
          <div>
            <Button disabled={disablePrimary} color="primary" onClick={() => handleClickPrimary()}>
              {primaryButton}
            </Button>{' '}
            <Button color="secondary" onClick={() => handleClickSecondary()}>
              {secondaryButton}
            </Button>
          </div>
        </ModalFooter>
      </Modal>
    </div>
  )
}

export default ModalSimple
