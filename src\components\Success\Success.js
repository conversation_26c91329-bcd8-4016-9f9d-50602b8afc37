import React from 'react';
import "./style.css";

export const Success = ({ color = "#68E534" }) => {
  return (<svg width="200" height="200" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
    <circle
      fill="none"
      stroke={color}
      strokeWidth="20"
      cx="200"
      cy="200"
      r="190"
      strokeLinecap="round"
      transform="rotate(-90 200 200)"
      className="circle"
    />
    <polyline
      fill="none"
      stroke={color}
      points="88,214 173,284 304,138"
      strokeWidth="24"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="tick"
    />
  </svg>);
}

export default Success;
