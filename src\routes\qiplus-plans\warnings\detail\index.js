/**
 * Warning Detail Component
 */
import moment from 'moment'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { connect } from 'react-redux'
import { Link, useHistory, useParams } from 'react-router-dom'

// Components
import { Tab, Tabs } from '@material-ui/core'
import LinearProgress from '@material-ui/core/LinearProgress'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import SimpleEditor from 'Components/Widgets/SimpleEditor'
import { Alert, Button, Form, FormGroup, Input, Label } from 'reactstrap'

// Helpers
import { mockWarnings, warningModel } from '../model'

const WarningDetail = (props) => {
  const history = useHistory()
  const { warningId } = useParams()
  const isEdit = warningId && warningId !== 'add'

  const [warning, setWarning] = useState({ ...warningModel })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState({})
  const [success, setSuccess] = useState(false)
  const [activeTab, setActiveTab] = useState(0)
  const [emailContentHtml, setEmailContentHtml] = useState('')
  const [editorHeight, setEditorHeight] = useState(350)

  // Refs para gerenciar responsividade
  const editorContainerRef = useRef(null)
  const cardContentRef = useRef(null)
  const resizeObserverRef = useRef(null)
  const ckEditorInstanceRef = useRef(null)

  // Callback ref para o card - garante que temos a referência quando o elemento é montado
  const cardRefCallback = useCallback((node) => {
    if (node) {
      cardContentRef.current = node
      console.log('Card ref set successfully:', node.className)
    }
  }, [])

  useEffect(() => {
    if (isEdit) {
      loadWarning()
    } else {
      // Set default author for new warnings
      setWarning(prev => ({
        ...prev,
        author: props.user?.displayName || props.user?.firstName || 'webmaster',
        date: moment().format('YYYY-MM-DDTHH:mm:ss'),
        modified: moment().format('YYYY-MM-DDTHH:mm:ss'),
      }))
    }
  }, [warningId, isEdit, props.user])

  // Sync email content HTML with warning content_mail
  useEffect(() => {
    setEmailContentHtml(warning.content_mail || '')
  }, [warning.content_mail])

  // Force CKEditor resize when tab changes and setup responsiveness
  useEffect(() => {
    if (activeTab === 1) {
      // Small delay to ensure the tab content is rendered
      setTimeout(() => {
        if (window.CKEDITOR) {
          for (const instanceName in window.CKEDITOR.instances) {
            const instance = window.CKEDITOR.instances[instanceName]
            if (instance) {
              instance.resize('100%', editorHeight)
            }
          }
        }

        // Configurar responsividade após renderização
        setupCKEditorEvents()
        adjustCardHeight()
      }, 200)
    } else {
      // Limpar altura mínima quando não estiver na aba do editor
      let cardElement = cardContentRef.current

      // Se a ref não funcionar, tentar encontrar pelo seletor
      if (!cardElement) {
        cardElement = document.querySelector('.dynamic-height-card')
      }

      if (cardElement && typeof cardElement.style !== 'undefined') {
        cardElement.style.minHeight = 'auto'
      }
    }
  }, [activeTab, editorHeight, setupCKEditorEvents, adjustCardHeight])

  // Cleanup ResizeObserver on unmount
  useEffect(() => {
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect()
      }
    }
  }, [])

  // Ajustar altura quando o conteúdo do editor muda
  useEffect(() => {
    if (activeTab === 1 && emailContentHtml) {
      setTimeout(adjustCardHeight, 300)
    }
  }, [emailContentHtml, activeTab, adjustCardHeight])

  // Responsividade para mudanças de tamanho da janela
  useEffect(() => {
    const handleResize = () => {
      if (activeTab === 1) {
        setTimeout(adjustCardHeight, 100)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [activeTab, adjustCardHeight])

  const loadWarning = () => {
    setLoading(true)

    // Simulate API call with mock data
    setTimeout(() => {
      const foundWarning = mockWarnings.find(w => w.id === warningId)
      if (foundWarning) {
        setWarning(foundWarning)
      }
      setLoading(false)
    }, 300)
  }

  const handleInputChange = (field, value) => {
    setWarning(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!warning.title?.trim()) {
      newErrors.title = 'Título é obrigatório'
    }

    if (!warning.content_mail?.trim()) {
      newErrors.content_mail = 'Conteúdo para email é obrigatório'
    }

    if (!warning.content_app?.trim()) {
      newErrors.content_app = 'Conteúdo para app é obrigatório'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm()) {
      return
    }

    setSaving(true)

    // Simulate API call
    setTimeout(() => {
      const updatedWarning = {
        ...warning,
        modified: moment().format('YYYY-MM-DDTHH:mm:ss'),
        id: warning.id || Date.now().toString(),
        ID: warning.ID || Date.now().toString(),
      }

      console.log('Saving warning:', updatedWarning)

      setSaving(false)
      setSuccess(true)

      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false)
        history.push('/qiplus-plans/warnings')
      }, 2000)
    }, 1000)
  }

  const handleCancel = () => {
    history.push('/qiplus-plans/warnings')
  }

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue)
  }

  const handleEmailContentChange = (html) => {
    setEmailContentHtml(html)
    handleInputChange('content_mail', html)
  }

  // Função para calcular e ajustar altura do card baseado no editor
  const adjustCardHeight = useCallback(() => {
    if (!editorContainerRef.current || activeTab !== 1) {
      return
    }

    const editorContainer = editorContainerRef.current

    // Obter altura real do editor
    const ckEditorElement = editorContainer.querySelector('.cke')
    if (!ckEditorElement) {
      return
    }

    const editorRect = ckEditorElement.getBoundingClientRect()

    // Calcular altura mínima necessária para o card
    const tabsHeight = 48 // altura das abas
    const labelHeight = 30 // altura do label
    const helpTextHeight = 35 // altura do texto de ajuda
    const cardPadding = 30 // padding interno do card
    const formGroupMargins = 20 // margens do form group

    const totalCardHeight = editorRect.height + tabsHeight + labelHeight + helpTextHeight + cardPadding + formGroupMargins

    // Tentar encontrar o elemento do card de diferentes formas
    let cardElement = cardContentRef.current

    // Se a ref não funcionar, tentar encontrar pelo seletor
    if (!cardElement) {
      cardElement = document.querySelector('.dynamic-height-card.editor-active')
    }

    // Como último recurso, encontrar o card pai do editor
    if (!cardElement && editorContainer) {
      cardElement = editorContainer.closest('.rct-block')
    }

    // Aplicar altura mínima diretamente ao card
    if (cardElement && typeof cardElement.style !== 'undefined') {
      cardElement.style.minHeight = `${totalCardHeight}px`
      cardElement.style.transition = 'min-height 0.3s ease-in-out'

      console.log('Card height adjusted successfully:', {
        editorHeight: editorRect.height,
        totalCardHeight,
        cardElement: cardElement.className || 'element found',
        activeTab
      })
    } else {
      console.warn('Card element not found for height adjustment:', {
        cardContentRef: !!cardContentRef.current,
        querySelector: !!document.querySelector('.dynamic-height-card.editor-active'),
        closestRctBlock: !!editorContainer?.closest('.rct-block')
      })
    }

    // Ajustar altura do container do editor
    const newEditorHeight = Math.max(350, editorRect.height)
    setEditorHeight(newEditorHeight)
  }, [activeTab])

  // Função para configurar ResizeObserver
  const setupResizeObserver = useCallback(() => {
    if (!window.ResizeObserver || !editorContainerRef.current || activeTab !== 1) {
      return
    }

    // Limpar observer anterior se existir
    if (resizeObserverRef.current) {
      resizeObserverRef.current.disconnect()
    }

    // Criar novo ResizeObserver
    resizeObserverRef.current = new ResizeObserver(() => {
      // Debounce para evitar muitas chamadas
      setTimeout(() => {
        adjustCardHeight()
      }, 150)
    })

    // Aguardar um pouco para garantir que o CKEditor foi renderizado
    setTimeout(() => {
      if (!editorContainerRef.current) return

      // Observar o container do editor e seus elementos internos
      const ckEditorElement = editorContainerRef.current.querySelector('.cke')
      const ckEditorInner = editorContainerRef.current.querySelector('.cke_inner')
      const ckEditorContents = editorContainerRef.current.querySelector('.cke_contents')

      let observedElements = 0

      if (ckEditorElement) {
        resizeObserverRef.current.observe(ckEditorElement)
        observedElements++
      }
      if (ckEditorInner) {
        resizeObserverRef.current.observe(ckEditorInner)
        observedElements++
      }
      if (ckEditorContents) {
        resizeObserverRef.current.observe(ckEditorContents)
        observedElements++
      }

      console.log(`ResizeObserver setup: observing ${observedElements} elements`)
    }, 200)
  }, [adjustCardHeight, activeTab])

  // Função para configurar eventos do CKEditor
  const setupCKEditorEvents = useCallback(() => {
    if (window.CKEDITOR && activeTab === 1) {
      // Aguardar um pouco para garantir que o editor foi criado
      setTimeout(() => {
        for (const instanceName in window.CKEDITOR.instances) {
          const instance = window.CKEDITOR.instances[instanceName]
          if (instance && !ckEditorInstanceRef.current) {
            ckEditorInstanceRef.current = instance

            // Eventos do CKEditor para detectar mudanças
            instance.on('instanceReady', () => {
              adjustCardHeight()
              setupResizeObserver()
            })

            instance.on('resize', () => {
              setTimeout(adjustCardHeight, 100)
            })

            instance.on('contentDom', () => {
              setTimeout(adjustCardHeight, 200)
            })

            instance.on('mode', () => {
              setTimeout(adjustCardHeight, 100)
            })

            break // Usar apenas a primeira instância encontrada
          }
        }
      }, 200)
    }
  }, [activeTab, adjustCardHeight, setupResizeObserver])

  return (
    <div className="data-table-wrapper warnings-detail">
      <div className="page-title d-flex justify-content-between align-items-center">
        <div className="page-title-wrap">
          <i className="ti-announcement"></i>
          <h2>
            <span>{isEdit ? 'Editar Aviso' : 'Novo Aviso'}</span>
          </h2>
        </div>
        <div className="page-title-right">
          <Link to="/qiplus-plans/warnings" className="btn btn-outline-secondary btn-sm mr-2">
            <i className="ti-arrow-left"></i> Voltar
          </Link>
        </div>
      </div>

      {(loading || saving) && <LinearProgress />}

      {success && (
        <Alert color="success" className="mb-3">
          <i className="ti-check mr-2"></i>
          Aviso {isEdit ? 'atualizado' : 'criado'} com sucesso!
        </Alert>
      )}

      <div className="main-content">
        <div className="row">
          <div className="col-md-8">
            {/* Título fora das abas */}
            <RctCollapsibleCard heading="Informações Básicas">
              <Form>
                <FormGroup>
                  <Label for="title">Título *</Label>
                  <Input
                    type="text"
                    id="title"
                    value={warning.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Digite o título do aviso"
                    invalid={!!errors.title}
                  />
                  {errors.title && <div className="invalid-feedback d-block">{errors.title}</div>}
                </FormGroup>
              </Form>
            </RctCollapsibleCard>

            {/* Abas para conteúdo */}
            <RctCollapsibleCard
              ref={cardRefCallback}
              customClasses={`dynamic-height-card ${activeTab === 1 ? 'editor-active' : ''}`}
              contentClasses="dynamic-content"
              heading={
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  indicatorColor="primary"
                  textColor="primary"
                >
                  <Tab label="Conteúdo para App" />
                  <Tab label="Conteúdo para Email" />
                </Tabs>
              }
            >
              {/* Aba 0: Conteúdo para App */}
              {activeTab === 0 && (
                <Form>
                  <FormGroup>
                    <Label for="content_app">Conteúdo para App *</Label>
                    <Input
                      type="textarea"
                      id="content_app"
                      rows="6"
                      value={warning.content_app}
                      onChange={(e) => handleInputChange('content_app', e.target.value)}
                      placeholder="Digite o conteúdo que será exibido no app (versão mais concisa)"
                      invalid={!!errors.content_app}
                    />
                    {errors.content_app && <div className="invalid-feedback d-block">{errors.content_app}</div>}
                    <small className="form-text text-muted">
                      Este conteúdo será exibido no aplicativo e deve ser mais conciso.
                    </small>
                  </FormGroup>
                </Form>
              )}

              {/* Aba 1: Conteúdo para Email com CKEditor */}
              {activeTab === 1 && (
                <Form>
                  <FormGroup>
                    <Label>Conteúdo para Email *</Label>
                    <div
                      ref={editorContainerRef}
                      className="editor-container dynamic-editor-container"
                      style={{
                        minHeight: `${editorHeight}px`,
                        width: '100%',
                        maxWidth: '100%',
                        overflow: 'hidden',
                        border: errors.content_mail ? '1px solid #dc3545' : '1px solid #ced4da',
                        borderRadius: '4px',
                        position: 'relative',
                        transition: 'min-height 0.3s ease-in-out'
                      }}>
                      <SimpleEditor
                        key={`email-editor-${activeTab}-${editorHeight}`}
                        content={emailContentHtml}
                        onChange={handleEmailContentChange}
                        height={editorHeight}
                        placeholder="Digite o conteúdo que será enviado por email (pode ser mais detalhado)"
                      />
                    </div>
                    {errors.content_mail && <div className="invalid-feedback d-block">{errors.content_mail}</div>}
                    <small className="form-text text-muted">
                      Este conteúdo será usado para envios por email e pode ser mais detalhado. Use a formatação rica para melhor apresentação.
                    </small>
                  </FormGroup>
                </Form>
              )}
            </RctCollapsibleCard>
          </div>

          <div className="col-md-4">
            <RctCollapsibleCard heading="Configurações">
              <Form>
                <FormGroup check className="mb-3">
                  <Label check>
                    <Input
                      type="checkbox"
                      checked={warning.active}
                      onChange={(e) => handleInputChange('active', e.target.checked)}
                    />
                    Aviso ativo
                  </Label>
                  <small className="form-text text-muted">
                    Define se o aviso está ativo e deve ser exibido.
                  </small>
                </FormGroup>

                <FormGroup>
                  <Label for="author">Autor</Label>
                  <Input
                    type="text"
                    id="author"
                    value={warning.author}
                    onChange={(e) => handleInputChange('author', e.target.value)}
                    placeholder="Nome do autor"
                    readOnly
                  />
                  <small className="form-text text-muted">
                    Responsável pela criação do aviso.
                  </small>
                </FormGroup>

                {isEdit && (
                  <>
                    <FormGroup>
                      <Label>Data de Criação</Label>
                      <div className="form-control-static">
                        {moment(warning.date).format('DD/MM/YYYY HH:mm')}
                      </div>
                    </FormGroup>

                    <FormGroup>
                      <Label>Última Modificação</Label>
                      <div className="form-control-static">
                        {moment(warning.modified).format('DD/MM/YYYY HH:mm')}
                      </div>
                    </FormGroup>
                  </>
                )}
              </Form>
            </RctCollapsibleCard>

            <RctCollapsibleCard heading="Ações">
              <div className="d-flex flex-column">
                <Button
                  color="primary"
                  onClick={handleSave}
                  disabled={saving}
                  className="mb-2"
                >
                  {saving ? (
                    <>
                      <i className="fa fa-spinner fa-spin mr-2"></i>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <i className="ti-save mr-2"></i>
                      {isEdit ? 'Atualizar Aviso' : 'Criar Aviso'}
                    </>
                  )}
                </Button>

                <Button
                  color="secondary"
                  onClick={handleCancel}
                  disabled={saving}
                >
                  <i className="ti-close mr-2"></i>
                  Cancelar
                </Button>
              </div>
            </RctCollapsibleCard>
          </div>
        </div>
      </div>
    </div>
  )
}

const mapStateToProps = ({ authReducer }) => {
  const { user, account, ownerId } = authReducer
  return { user, account, accountId: account?.ID, ownerId }
}

export default connect(mapStateToProps)(WarningDetail)
