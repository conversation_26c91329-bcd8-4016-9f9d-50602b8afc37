/**
 * Warning Detail Component
 */
import React, { useState, useEffect } from 'react'
import { connect } from 'react-redux'
import { Link, useHistory, useParams } from 'react-router-dom'
import moment from 'moment'

// Components
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import { Button, Form, FormGroup, Label, Input, Alert } from 'reactstrap'
import LinearProgress from '@material-ui/core/LinearProgress'

// Helpers
import { warningModel, mockWarnings } from '../model'

const WarningDetail = (props) => {
  const history = useHistory()
  const { warningId } = useParams()
  const isEdit = warningId && warningId !== 'add'

  const [warning, setWarning] = useState({ ...warningModel })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState({})
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    if (isEdit) {
      loadWarning()
    } else {
      // Set default author for new warnings
      setWarning(prev => ({
        ...prev,
        author: props.user?.displayName || props.user?.firstName || 'webmaster',
        date: moment().format('YYYY-MM-DDTHH:mm:ss'),
        modified: moment().format('YYYY-MM-DDTHH:mm:ss'),
      }))
    }
  }, [warningId, isEdit, props.user])

  const loadWarning = () => {
    setLoading(true)
    
    // Simulate API call with mock data
    setTimeout(() => {
      const foundWarning = mockWarnings.find(w => w.id === warningId)
      if (foundWarning) {
        setWarning(foundWarning)
      }
      setLoading(false)
    }, 300)
  }

  const handleInputChange = (field, value) => {
    setWarning(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!warning.title?.trim()) {
      newErrors.title = 'Título é obrigatório'
    }
    
    if (!warning.content_mail?.trim()) {
      newErrors.content_mail = 'Conteúdo para email é obrigatório'
    }
    
    if (!warning.content_app?.trim()) {
      newErrors.content_app = 'Conteúdo para app é obrigatório'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (!validateForm()) {
      return
    }

    setSaving(true)
    
    // Simulate API call
    setTimeout(() => {
      const updatedWarning = {
        ...warning,
        modified: moment().format('YYYY-MM-DDTHH:mm:ss'),
        id: warning.id || Date.now().toString(),
        ID: warning.ID || Date.now().toString(),
      }
      
      console.log('Saving warning:', updatedWarning)
      
      setSaving(false)
      setSuccess(true)
      
      // Hide success message after 3 seconds
      setTimeout(() => {
        setSuccess(false)
        history.push('/qiplus-plans/warnings')
      }, 2000)
    }, 1000)
  }

  const handleCancel = () => {
    history.push('/qiplus-plans/warnings')
  }

  return (
    <div className="data-table-wrapper">
      <div className="page-title d-flex justify-content-between align-items-center">
        <div className="page-title-wrap">
          <i className="ti-announcement"></i>
          <h2>
            <span>{isEdit ? 'Editar Aviso' : 'Novo Aviso'}</span>
          </h2>
        </div>
        <div className="page-title-right">
          <Link to="/qiplus-plans/warnings" className="btn btn-outline-secondary btn-sm mr-2">
            <i className="ti-arrow-left"></i> Voltar
          </Link>
        </div>
      </div>

      {(loading || saving) && <LinearProgress />}
      
      {success && (
        <Alert color="success" className="mb-3">
          <i className="ti-check mr-2"></i>
          Aviso {isEdit ? 'atualizado' : 'criado'} com sucesso!
        </Alert>
      )}

      <div className="main-content">
        <div className="row">
          <div className="col-md-8">
            <RctCollapsibleCard heading="Conteúdo do Aviso">
              <Form>
                <FormGroup>
                  <Label for="title">Título *</Label>
                  <Input
                    type="text"
                    id="title"
                    value={warning.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Digite o título do aviso"
                    invalid={!!errors.title}
                  />
                  {errors.title && <div className="invalid-feedback d-block">{errors.title}</div>}
                </FormGroup>

                <FormGroup>
                  <Label for="content_mail">Conteúdo para Email *</Label>
                  <Input
                    type="textarea"
                    id="content_mail"
                    rows="6"
                    value={warning.content_mail}
                    onChange={(e) => handleInputChange('content_mail', e.target.value)}
                    placeholder="Digite o conteúdo que será enviado por email (pode ser mais detalhado)"
                    invalid={!!errors.content_mail}
                  />
                  {errors.content_mail && <div className="invalid-feedback d-block">{errors.content_mail}</div>}
                  <small className="form-text text-muted">
                    Este conteúdo será usado para envios por email e pode ser mais detalhado.
                  </small>
                </FormGroup>

                <FormGroup>
                  <Label for="content_app">Conteúdo para App *</Label>
                  <Input
                    type="textarea"
                    id="content_app"
                    rows="4"
                    value={warning.content_app}
                    onChange={(e) => handleInputChange('content_app', e.target.value)}
                    placeholder="Digite o conteúdo que será exibido no app (versão mais concisa)"
                    invalid={!!errors.content_app}
                  />
                  {errors.content_app && <div className="invalid-feedback d-block">{errors.content_app}</div>}
                  <small className="form-text text-muted">
                    Este conteúdo será exibido no aplicativo e deve ser mais conciso.
                  </small>
                </FormGroup>
              </Form>
            </RctCollapsibleCard>
          </div>

          <div className="col-md-4">
            <RctCollapsibleCard heading="Configurações">
              <Form>
                <FormGroup check className="mb-3">
                  <Label check>
                    <Input
                      type="checkbox"
                      checked={warning.active}
                      onChange={(e) => handleInputChange('active', e.target.checked)}
                    />
                    Aviso ativo
                  </Label>
                  <small className="form-text text-muted">
                    Define se o aviso está ativo e deve ser exibido.
                  </small>
                </FormGroup>

                <FormGroup>
                  <Label for="author">Autor</Label>
                  <Input
                    type="text"
                    id="author"
                    value={warning.author}
                    onChange={(e) => handleInputChange('author', e.target.value)}
                    placeholder="Nome do autor"
                    readOnly
                  />
                  <small className="form-text text-muted">
                    Responsável pela criação do aviso.
                  </small>
                </FormGroup>

                {isEdit && (
                  <>
                    <FormGroup>
                      <Label>Data de Criação</Label>
                      <div className="form-control-static">
                        {moment(warning.date).format('DD/MM/YYYY HH:mm')}
                      </div>
                    </FormGroup>

                    <FormGroup>
                      <Label>Última Modificação</Label>
                      <div className="form-control-static">
                        {moment(warning.modified).format('DD/MM/YYYY HH:mm')}
                      </div>
                    </FormGroup>
                  </>
                )}
              </Form>
            </RctCollapsibleCard>

            <RctCollapsibleCard heading="Ações">
              <div className="d-flex flex-column">
                <Button
                  color="primary"
                  onClick={handleSave}
                  disabled={saving}
                  className="mb-2"
                >
                  {saving ? (
                    <>
                      <i className="fa fa-spinner fa-spin mr-2"></i>
                      Salvando...
                    </>
                  ) : (
                    <>
                      <i className="ti-save mr-2"></i>
                      {isEdit ? 'Atualizar Aviso' : 'Criar Aviso'}
                    </>
                  )}
                </Button>
                
                <Button
                  color="secondary"
                  onClick={handleCancel}
                  disabled={saving}
                >
                  <i className="ti-close mr-2"></i>
                  Cancelar
                </Button>
              </div>
            </RctCollapsibleCard>
          </div>
        </div>
      </div>
    </div>
  )
}

const mapStateToProps = ({ authReducer }) => {
  const { user, account, ownerId } = authReducer
  return { user, account, accountId: account?.ID, ownerId }
}

export default connect(mapStateToProps)(WarningDetail)
