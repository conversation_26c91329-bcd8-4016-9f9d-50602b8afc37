/**
 * Page Title Bar Component
 * Used To Display Page Title & Breadcrumbs
 */
import React, { Component } from 'react'
import { Link } from 'react-router-dom'
import { Breadcrumb, BreadcrumbItem } from 'reactstrap'

// intl messages
import { <PERSON>Field, Tooltip } from '@material-ui/core'
import IntlMessages from 'Util/IntlMessages'
import { langMessages } from '../../lang'

// get display string
const getDisplayString = sub => {
  const arr = sub.split('-')
  const key = sub.charAt(0) + sub.slice(1) || 'app'
  if (langMessages[`sidebar.${key}`]) {
    return <span>{langMessages[`sidebar.${key}`]}</span>
  } else if (arr.length > 1) {
    return <IntlMessages id={`sidebar.${arr[0].charAt(0) + arr[0].slice(1) + arr[1].charAt(0).toUpperCase() + arr[1].slice(1)}`} />
  } else {
    return <IntlMessages id={`sidebar.${key}`} />
  }
}

// get url string
const getUrlString = (path, sub, index) => {
  if (index === 0) {
    return '/'
  } else {
    return '/' + path.split(sub)[0] + sub
  }
}

class PageTitleBar extends Component {
  titleChangeTimeout = null

  state = {
    titleValue: typeof this.props.title === 'string' ? this.props.title : '',
    hasFocus: false,
  }

  handleChange({ target: { value } }) {
    const titleValue = value
    this.setState({ titleValue, hasFocus: true })
    clearTimeout(this.titleChangeTimeout)
    this.titleChangeTimeout = setTimeout(() => this.props.onUpdate && this.props.onUpdate(titleValue), 300)
  }

  handleFocus(hasFocus) {
    let titleValue = typeof this.props.title === 'string' ? this.props.title : ''
    setTimeout(() => this.setState({ hasFocus, titleValue }), 100)
  }

  componentDidUpdate(prevProps) {
    if (this.props.title !== prevProps.title && this.props.title !== this.state.titleValue) {
      this.setState({ titleValue: this.props.title })
    }
  }
  /* shouldComponentUpdate(props,state) {
      return (typeof props.title === 'string' ? props.title !== state.titleValue : true)
   } */

  goBack() {
    const { history, onBackClick } = this.props
      ; (onBackClick && onBackClick()) || (history && history.goBack())
  }

  render() {
    let { titleValue, hasFocus } = this.state
    const {
      match: { path },
      editable,
      enableBreadCrumb,
      middleComponents,
      rightComponents,
      badgeClass,
      tooltip,
      tooltipTitle
    } = this.props

    const subPath = !!path ? path.substr(1).split('/') : []

    let { title, placeholder, icon, badge, subtitle } = this.props

    if (!title) {
      title = langMessages['components.title']
      badge = ''
      hasFocus = !!editable
    }

    return (
      <div className="page-title d-flex justify-content-between align-items-center flex-1" id="page-title">
        <div className={`page-title-wrap flex-1 d-flex justify-content-start align-items-center ${(!!editable && 'editable') || ''}`}>
          {(this.props.history || this.props.onBackClick) && <i className="cursor-pointer ti-angle-left" onClick={e => this.goBack()}></i>}
          {/* { icon && <i className={`${icon} font-lg mr-10`}></i>} */}
          {!!editable && !!hasFocus && (
            <TextField
              fullWidth
              autoFocus
              id="title"
              placeholder={placeholder || langMessages['components.title']}
              value={titleValue || ''}
              onFocus={e => e.target.select()}
              onBlur={e => this.handleFocus(false)}
              onChange={this.handleChange.bind(this)}
            />
          )}
          {(!editable || !hasFocus) && (
            <div className="d-flex flex-column justify-content-start align-items-start">
              <h2 className="page-title-h2" onClick={e => !!editable && this.handleFocus(true)}>
                {(typeof title === 'string' && <span className="page-title-span">{title || ''}</span>) || title || langMessages['components.title']}
                {!!badge && <b className={`align-top font-xs text-muted ml-1 ${badgeClass || ''}`}>{badge}</b>}
              </h2>
              {!!subtitle && <div className="subtitle flex-1 d-flex justify-content-center align-items-center">{subtitle}</div>}
            </div>
          )}
          {tooltip && <Tooltip classes={{ tooltip: 'font-xs bg-dark' }} placement="right-start" title={<span style={{ whiteSpace: "pre-line" }}> {tooltipTitle}</span>}>
            <small className="align-top">
              <i className="fa fa-question-circle ml-5" />
            </small>
          </Tooltip>}

        </div>
        {!!middleComponents && <div className="middleComponents flex-1 d-flex justify-content-center align-items-center">{middleComponents}</div>}
        {!!rightComponents && <div className="rightComponents flex-1 d-flex justify-content-end align-items-center">{rightComponents}</div>}
        {enableBreadCrumb && (
          <Breadcrumb className="mb-0 tour-step-7" tag="nav">
            {subPath.map((sub, index) => {
              return (
                (sub.charAt(0) !== ':' && (
                  <BreadcrumbItem
                    active={subPath.length === index + 1}
                    tag={subPath.length === index + 1 ? 'span' : Link}
                    key={index}
                    to={getUrlString(path, sub, index)}
                  >
                    {getDisplayString(sub)}
                  </BreadcrumbItem>
                )) ||
                null
              )
            })}
          </Breadcrumb>
        )}
      </div>
    )
  }
}

// default props value
PageTitleBar.defaultProps = {
  enableBreadCrumb: false,
}

export default PageTitleBar
