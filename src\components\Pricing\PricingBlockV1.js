/**
 * Pricing Component
 */
import React from 'react'
import { Button } from 'reactstrap'

// component
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'

// intl messages
import IntlMessages from 'Util/IntlMessages'

const PricingBlockV1 = ({
  flag,
  flagClasses,
  title,
  subtitle,
  startingAt,
  description,
  price,
  smallPrice,
  features,
  color,
  buttonText,
  onChoose,
  colClasses,
  children,
  disabled,
  noImg,
  imgSrc,
  imgWidth,
  imgHeight,
  imgWrapperClasses,
}) => (
  <RctCollapsibleCard customClasses="PricingBlockV1 pricing-block-v1 text-center" colClasses={colClasses || 'col-md-4'}>
    {!!flag && (
      <div className={`plan-flag ${flagClasses || ''}`}>
        <span>{flag}</span>
      </div>
    )}
    {noImg !== true && (
      <div className={`pricing-icon mb-40 ${imgWrapperClasses || ''}`}>
        <img
          src={imgSrc || require('Assets/img/pricing-icon.png')}
          alt="pricing icon"
          className="img-fluid"
          width={imgWidth || ''}
          height={imgHeight || ''}
        />
      </div>
    )}
    <h2 className={`text-${color} pricing-title`}>{title}</h2>
    {typeof description === 'string' ? <p>{description}</p> : description}
    {!!subtitle && (
      <div className="plan-info">
        <span>{subtitle}</span>
      </div>
    )}
    {children}
    {!!startingAt && <span className="text-muted mb-5 d-block small">{startingAt}</span>}
    {!!(price || smallPrice) && (
      <div className="mb-25">
        <h2 className="amount-title">{price}</h2>
        <span className="text-muted small">{smallPrice}</span>
      </div>
    )}
    {!!features && !!features.length && (
      <ul className="price-detail list-unstyled">
        {features.map((feature, key) => (
          <li key={key}>{feature.title}</li>
        ))}
      </ul>
    )}
    {!!buttonText && (
      <Button disabled={disabled === true} color={color} className="btn-block btn-lg" onClick={onChoose}>
        {buttonText}
      </Button>
    )}
  </RctCollapsibleCard>
)

export default PricingBlockV1
