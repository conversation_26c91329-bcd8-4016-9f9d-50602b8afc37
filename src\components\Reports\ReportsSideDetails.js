import React from "react";
import Alert from "reactstrap/lib/Alert";

const ReportsSideDetails = (props) => {
  const { data } = props;

  console.log("ReportsSideDetails props", props);
  console.log("ReportsSideDetails", data);

  const show = data.length > 0 && data[0]?.title ? true : false;
  const showDescription =
    data.length > 0 && data[0]?.description && !data[0]?.title ? true : false;

  return (
    <div className="d-flex flex-column justify-content-around flex-1 p-10 ">
      {show && (
        <>
          <div className="d-flex align-items-center mt-20 mb-20">
            <p>{`Detalhando ${data[0]?.sub_title}`}</p>
          </div>
          <div className="d-flex align-items-center">
            <span>{data[0]?.title}: </span>
            <b>{data[0]?.sum}</b>
          </div>
          <div className="d-flex align-items-center">
            <span>{data[1]?.title}: </span>
            <b>{data[1]?.sum}</b>
          </div>
          <div className="d-flex align-items-center">
            <span>{data[2]?.title}: </span>
            <b>{data[2]?.sum}</b>
          </div>
          {data[3] && (
            <div className="d-flex align-items-center mt-5 border-top">
              <div className="d-flex align-items-center">
                <span>{data[3]?.title}: </span>
                <b>{data[3]?.sum}</b>
              </div>
            </div>
          )}
          {data[0]?.description && (
            <>
              <div className="d-flex align-items-center">
                <Alert color="primary">{data[0]?.description}</Alert>
              </div>
            </>
          )}
        </>
      )}
      {showDescription && (
        <>
          <div className="d-flex align-items-center">
            <Alert color="primary">{data[0]?.description}</Alert>
          </div>
        </>
      )}
    </div>
  );
};

export default ReportsSideDetails;
