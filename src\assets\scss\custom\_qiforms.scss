/* 
FORMS
*/
.form-canvas {
    .qi-icon-button {
        cursor: move;
        display: flex;
        border-width: 2px;
        margin-bottom: 1em;
        .input-wrapper {
            margin-top: 1em;
            label {
                margin: 0 0 .5em;
            }    
            label[for="required"] {
                // display: none;
            }
        }
        .MuiOutlinedInput-input {
            padding: 12px 12px;
        }
        .MuiInputLabel-outlined:not(.MuiInputLabel-shrink) {
            transform: translate(15px, calc(100% - 2px));
        }
        .btn-label small,
        .toggled-label small {
            text-overflow: ellipsis;
            display: block;
            overflow: hidden;
            white-space: nowrap;
            max-width: 260px;
        }
        &.toggled {
            max-height: 2.1rem;
            height: 2.1rem;
            .toggled-label {
                display: flex;
                align-items: center;
                padding: 0 4.175rem;
                width: 100%;
            }
            .alert-addon i {
                font-size: .9rem;
            }
        }
    }
    .form-fields-wrapper {
        height: 100%;
        border: 2px dashed #d4d4d4;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        flex: 1;
        padding: 5px;
        background: $light-gray-color;
        .draggable-item {
            max-width: 600px;
            min-width: 180px;
            width: 100%;
            align-self: center;
        }
    }
    > div[style*="overflow:scroll"],
    > div[style*="overflow: scroll"] {
        display: flex;
    }
}

.form-fields-block {
    .isDragDisabled {
        .qi-icon-button {
            cursor: not-allowed;
        }
    }
}
