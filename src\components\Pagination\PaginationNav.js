import React from 'react'

import { Input, Pagination, PaginationItem, PaginationLink } from 'reactstrap'

const paginationIndex = (toPage, lastPage, maxPages) => {
  const navBtnIndex = []
  const minPage = lastPage - Math.ceil(maxPages / 2) > maxPages ? Math.max(1, toPage - Math.ceil(maxPages / 2)) : 1

  toPage > 1 && navBtnIndex.push(toPage - 1) // prev
  minPage > 1 && navBtnIndex.push(1) // page 1
  for (let p = minPage; p <= lastPage; p++) {
    navBtnIndex.push(p) // page p
    if (p === minPage + maxPages) {
      if (toPage > p) {
        navBtnIndex.push(toPage) // page current
      }
      if (toPage < lastPage) {
        navBtnIndex.push(lastPage) // page Last
      }
      break
    }
  }
  toPage < lastPage && navBtnIndex.push(toPage + 1) // next
  return navBtnIndex
}

const PaginationNav = ({ page, pages, maxPages, goToPage, hasInput }) => {
  !maxPages && (maxPages = 10)

  const hasMore = pages - page > 5 // more than 3 pages
  const paginationBtns = []

  const currentPage = parseInt(page, 10)
  const lastPage = parseInt(pages, 10)
  let minPage = lastPage - Math.ceil(maxPages / 2) > maxPages ? Math.max(1, currentPage - Math.ceil(maxPages / 2)) : 1

  if (minPage > 1) {
    paginationBtns.push(
      <PaginationItem key={1}>
        <PaginationLink onClick={e => goToPage(1, paginationIndex(1, lastPage, maxPages))}>1</PaginationLink>
      </PaginationItem>
    )
    paginationBtns.push(
      <PaginationItem key={2}>
        <PaginationLink onClick={e => e.preventDefault()}>...</PaginationLink>
      </PaginationItem>
    )
    if (lastPage - minPage < maxPages) {
      minPage -= maxPages - (lastPage - minPage)
    }
  }

  for (let p = minPage + paginationBtns.length; p <= lastPage; p++) {
    paginationBtns.push(
      <PaginationItem active={currentPage === p} key={p}>
        <PaginationLink onClick={e => goToPage(p, paginationIndex(p, lastPage, maxPages))}>{p}</PaginationLink>
      </PaginationItem>
    )
    if (p === minPage + maxPages && p < lastPage) {
      paginationBtns[paginationBtns.length - 2] = (
        <PaginationItem key={0}>
          <PaginationLink onClick={e => e.preventDefault()}>...</PaginationLink>
        </PaginationItem>
      )
      paginationBtns[paginationBtns.length - 1] = (
        <PaginationItem key={lastPage}>
          <PaginationLink onClick={e => goToPage(lastPage, paginationIndex(lastPage, lastPage, maxPages))}>{lastPage}</PaginationLink>
        </PaginationItem>
      )
      break
    }
  }

  return (
    (pages > 1 && (
      <div className="d-flex justify-content-between mb-0 px-10 py-10">
        <Pagination>
          <PaginationItem disabled={currentPage === 1}>
            <PaginationLink previous onClick={e => goToPage(currentPage - 1, paginationIndex(currentPage - 1, lastPage, maxPages))} />
          </PaginationItem>
          {paginationBtns}
          {!!hasMore && (
            <PaginationItem key={lastPage + 1}>
              <PaginationLink onClick={e => e.preventDefault()}>...</PaginationLink>
            </PaginationItem>
          )}
          <PaginationItem disabled={currentPage === lastPage}>
            <PaginationLink next onClick={e => goToPage(currentPage + 1, paginationIndex(currentPage + 1, lastPage, maxPages))} />
          </PaginationItem>
        </Pagination>
        {(hasInput && (
          <Pagination>
            <PaginationItem className="d-flex">
              <Input
                style={{ maxWidth: '60px', textAlign: 'center', padding: '0', lineHeight: '35px' }}
                type="number"
                value={currentPage}
                onChange={e => goToPage(e.target.value, paginationIndex(e.target.value, lastPage, maxPages))}
              />
            </PaginationItem>
          </Pagination>
        )) ||
          null}
      </div>
    )) ||
    null
  )
}

export default PaginationNav
