import { <PERSON><PERSON>, Link } from "@material-ui/core";
import React from 'react';

// lang strings
import { langMessages } from '../../lang';
// delete confirmation dialog
import DeleteConfirmationDialog from 'Components/DeleteConfirmationDialog/DeleteConfirmationDialog';

export const GridCardFooter = ({
  title,
  collection,
  name = '',
  avatar,
  actions = [],
  onEdit,
  onDelete,
  deleteMessage,
}) => {

  const confirmationRef = React.createRef()
  const handleDelete = () => {
    confirmationRef.current.open()
  }

  actions = actions.concat([
    {
      name: langMessages['button.edit'],
      // icon: 'fa fa-pencil', // optional
      onClick: onEdit,
      hidden: !onEdit,
    },
    {
      name: langMessages['button.delete'],
      // icon: 'fa fa-trash', // optional
      // className: 'text-danger', // optional
      onClick: handleDelete,
      hidden: !onDelete,
    }
  ])
  const actionsFiltered = actions && actions.filter(a => !a.hidden) || []

  const isNotLast = index => index < actionsFiltered.length - 1
  const defaultClassName = 'text-primary text-capitalize'

  return (<div className="rct-block-footer-wrapper d-flex justify-content-between align-items-center">
    <UserProfile name={name} avatar={avatar} />
    <div className="text-right">
      {actionsFiltered.map(({ name, icon, onClick, className = defaultClassName }, index) => (
        <Link key={name} onClick={onClick} className={`${className} ${isNotLast(index) ? 'mr-5' : ''}  fs-14`}>
          {name && name}
          {icon && <i className={`${icon} ml-5`} />}
          {isNotLast(index) && <span> | </span>}
        </Link>
      ))}
    </div>
    <DeleteConfirmationDialog
      ref={confirmationRef}
      title={
        (title && langMessages['placeholders.confirmRemoval'].replace('[%s]', title)) || langMessages[`${collection}.confirmRemoval`]
      }
      message={deleteMessage || langMessages[`${collection}.willBeSentToTrash`]}
      onConfirm={onDelete}
    />
  </div>
  );
}

const UserProfile = ({ name, avatar }) => {
  return (
    <div className="project-author">
      <div className="author-img-wrap d-flex flex-wrap">
        <div className="media-left media-middle mr-15 d-flex align-items-center" key={`${name}`} title={name}>
          {(name || avatar) && <UserAvatar name={name} avatar={avatar} />}
          <span>{name}</span>
        </div>
      </div>
    </div>
  )
}

const UserAvatar = ({ name, avatar }) => {
  const initials = `${name}`.split(' ').map(n => n[0]).join('')

  if (avatar && avatar.length > 0) {
    return (
      <Avatar title={name} className="rounded-circle bg-qiplus text-white mr-5 mini-avatar">
        <img src={avatar} alt={name} className="img-fluid" width="40" />
      </Avatar>
    )
  }

  return <Avatar className="rounded-circle bg-qiplus text-white mr-5 mini-avatar"></Avatar>
}
