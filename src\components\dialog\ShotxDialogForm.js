import {
  Avatar,
  Box,
  Button,
  ButtonGroup,
  Checkbox,
  Chip,
  DialogContent,
  FormControl,
  FormControlLabel,
  FormGroup,
  InputAdornment,
  InputLabel,
  makeStyles,
  MenuItem,
  OutlinedInput,
  Select,
  TextField,
  Typography
} from "@material-ui/core";
import { Alert } from "@material-ui/lab";
import React, { useState } from 'react';
import { Label } from "reactstrap";
import { HorizontalGroup } from "./HorizontalGroup";
import { SwitchOption } from "./SwitchOption";

const useStyles = makeStyles((theme) => ({
  label: {
    margin: theme.spacing(1),
    justifyContent: 'center',
    alignItems: 'center',
  },
  formControl: {
    margin: theme.spacing(1),
    width: '90%',
  },
  selectEmpty: {
    marginTop: theme.spacing(2),
  },
  alert: {
    width: '90%',
    margin: theme.spacing(1),
    justifyContent: 'start',
    alignItems: 'center',
  },
  chip: {
    marginRight: theme.spacing(1),
  }
}));

export const ShotxDialogForm = ({ inputs, title, description, buttons, children, alignH = 'center', alignV = 'center', subtitle }) => {
  const classes = useStyles();

  const getField = (input, index) => {

    const {
      type,
      name,
      label,
      value,
      onChange,
      checked = false,
      onChecked,
      options,
      disabled,
      display = 'inherit',
      prefix,
      checkTooltip = 'Enable',
      values,
      color,
      variant,
      onClick,
      onRemove
    } = input

    const tooltipWidth = checkTooltip.length * 10
    const key = `input-${index}`
    switch (type) {
      case 'text':
        return (
          <TextField
            key={key}
            // margin='normal'
            variant='outlined'
            fullWidth
            autoFocus
            id={name}
            placeholder={label}
            label={label}
            defaultValue={value}
            onChange={onChange}
            className={classes.formControl}
          />
        );
      case 'select':
        return (
          <FormControl variant="outlined" className={classes.formControl} key={key} disabled={disabled}>
            <InputLabel id={`label-${name}`}>{label}</InputLabel>
            <Select
              labelId={`label-${name}`}
              id={name}
              value={value || ''}
              onChange={disabled ? null : onChange}
              disabled={disabled}
              label={label}
            >
              {options.map((option, index) => (
                <MenuItem key={`${name}-option-${index}`} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      case 'multiple-select':
        return (
          <FormControl variant="outlined" key={key} className={classes.formControl}>
            <InputLabel id={`label-${name}`}>{label}</InputLabel>
            <Select
              labelId={`label-${name}`}
              id={name}
              multiple
              value={value || []}
              onChange={onChange}
              label={label}
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip
                      key={value}
                      className={classes.chip}
                      label={options.find((option) => option.value === value)?.label}
                      onMouseDown={(e) => {
                        e.stopPropagation();
                      }}
                      onDelete={(e) => {
                        e.stopPropagation();
                        onRemove(value)
                      }}
                    />
                  ))}
                </Box>
              )}
            >
              {options.filter((option) => !value.includes(option.value)).map((option, index) => (
                <MenuItem key={`${name}-option-${index}`} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )
      case 'input-checkbox':
        return (

          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled} style={{ 'display': display }}>
            <InputLabel htmlFor={`label-${name}`}>{label}</InputLabel>
            <OutlinedInput
              id={`label-${name}`}
              type={'text'}
              value={value || ''}
              onChange={onChange}
              disabled={disabled}
              startAdornment={!!prefix ? <InputAdornment position="start">{prefix}</InputAdornment> : null}
              endAdornment={
                <InputAdornment position="end">
                  <FormControlLabel
                    className={classes.label}
                    control={
                      <Checkbox
                        checked={checked}
                        onChange={disabled ? null : onChecked}
                        name={`${name}Enable`}
                        disabled={disabled}
                      />
                    }
                    label={checkTooltip}
                  />
                </InputAdornment>
              }
              labelWidth={tooltipWidth}
            />
          </FormControl>
        )
      case 'checkbox':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <FormControlLabel
              key={key}
              control={
                <Checkbox
                  checked={checked}
                  onChange={disabled ? null : onChecked}
                  name={name}
                  disabled={disabled}
                />
              }
              label={label}
            />
          </FormControl >
        )
      case 'buttons':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <Label for={`label-${name}`}>{label}</Label>
            <ButtonGroup aria-label={`outlined button group"`} disabled={disabled}>
              {values.map(({ label, value, disabled = false }, index) => (
                <Button
                  key={`button-${label}-${index}`}
                  variant="outlined"
                  onClick={() => onClick(value)}
                  disabled={disabled}
                >
                  {label}
                </Button>
              ))}
            </ButtonGroup>
          </FormControl>
        )
      case 'group-horizontal':
        return <HorizontalGroup
          key={`group-horizontal-${index}`}
          input={input}
          childs={values?.map((inputChild, index) => getField(inputChild, index))}
        />
      case 'switch':
        return <SwitchOption key={index} input={input} />
      case 'group':
        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" disabled={disabled}>
            <Label for={`label-${name}`}>{label}</Label>
            <FormGroup aria-label={`outlined button group"`} disabled={disabled}>
              {values.map((inputChild, index) => getField(inputChild, index))}
            </FormGroup>
          </FormControl>
        )
      case 'button-instagram':
        const [hasConected, setHasConected] = useState(false)

        return (
          <FormControl className={classes.formControl} key={key} variant="outlined" style={{ 'display': display }} disabled={disabled}>

            <ButtonGroup aria-label={`outlined button group"`} disabled={disabled}>

              <Button
                key={`button-${label}-${index}`}
                variant="outlined"
                onClick={() => onClick(value)} // {() => setHasConected(!hasConected)} //
                disabled={disabled}
                border='1px solid'
                borderColor="bg-gradient-to-r from-[#833ab4] via-[#fd1d1d] to-[#fcb045]"
              >
                {hasConected ? (
                  <span
                    className="d-flex align-items-center text-black"
                  >
                    <Avatar
                      src={require(`Assets/img/gallery-4.jpg`)}
                      className="mr-10"
                      style={{ width: 40, height: 40, background: '#fff' }}
                      variant="rounded"
                    />
                    <b>Desconectar ao Instagram</b>
                  </span>
                ) : (
                  <span
                    className="d-flex align-items-center text-black"
                  >
                    <Avatar
                      src={require(`Assets/img/instagram.png`)}
                      className="mr-10"
                      style={{ width: 40, height: 40, background: '#fff' }}
                      variant="rounded"
                    />
                    <b>Conectar ao Instagram</b>
                  </span>)}

              </Button>
            </ButtonGroup>
          </FormControl>
        )
      case 'alert':
        return (
          <Alert
            key={key}
            severity={color}
            variant={variant}
            className={classes.alert}
          >
            {label}
          </Alert>
        )
      default:
        console.warn(`Invalid input type: ${type}`)
        return null;
    }
  }

  return (
    <DialogContent dividers className={`d-flex flex-column align-items-${alignH} justify-content-${alignV} `}>
      {title && (
        <Typography gutterBottom variant='h5'>
          {title}
        </Typography>
      )}

      {description && (
        <Typography gutterBottom variant='body1'>
          {description}
        </Typography>
      )}

      {children && children}

      {inputs && inputs.filter((input) => !input.hidden).map((getField))}

      {subtitle && (
        <Typography gutterBottom variant='body1'>
          {subtitle}
        </Typography>
      )}

      {buttons && buttons.filter((button) => !button.hidden).map((button, index) => (
        <Button
          key={`button-${index}`}
          variant="contained"
          color="primary"
          onClick={button.onClick}
          disabled={button.disabled}
        >
          {button.text}
        </Button>
      ))}

    </DialogContent>
  );
}
