// list styles

.tasklist-wrapper {
    padding: 1rem;
    border: 1px solid #efefef;
    border-radius: 5px;
    .tasklist-title {
        .delete-btn {
            position: relative;
            top: -5px;
            right: -5px;            
            i {
                font-weight: 600;
            }
        }
        .MuiInputBase-input {
            font-weight: 500;
            padding: 0;
        }
        .MuiInput-underline:before, 
        .MuiInput-underline:after {
            display: none;
        }
    }
    .tasklist-list {
        > li {
            + li { 
                margin-top: -10px;
            }
            .list-item-checker {
                label {
                    margin-right: 0;
                    margin-bottom: 0;
                }
                .MuiCheckbox-root.Mui-disabled {
                    color: rgba(0, 0, 0, 0.1);
                }
            }
            .list-item-body {
                .task-title {
                    min-height: 20px;
                    min-width: 200px;
                }
                .MuiInputBase-input {
                    font-family: "Heebo", sans-serif;
                    font-weight: 500;
                    color: #464D69;
                    font-size: 15px;
                    background: #f7f7f7;
                    border-bottom: 1px dashed #d6d6d6;
                    padding: 5px 5px 3px 5px;
                    margin-left: -5px;
                }
                .MuiInput-underline:before, 
                .MuiInput-underline:after {
                    display: none;
                }
            }
        }
        &.editable-list {
            > li {
                margin-top: 0;
                &:not(:last-of-type) {
                    margin-bottom: -10px;
                }
            }
        }
    }
}
.projects-wrapper {
    .tasklist-wrapper {
        border: 0;
        padding: 1.5rem;
    }
}