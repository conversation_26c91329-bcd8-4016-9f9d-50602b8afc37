// pipeline styles
.pipeline-wrap {
    .rct-block-title {
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        .contextual-link {
            right: 50px;
        }
    }
    tr.gu-transit td {
        background: #ffffdc;
    }
    tr.gu-transit {
        box-shadow: 0 0 5px rgba($color: #000000, $alpha: 0.3);
    }
    tr.draggable-item:not(.isDragging) {
        transform: none !important;
    }
}
