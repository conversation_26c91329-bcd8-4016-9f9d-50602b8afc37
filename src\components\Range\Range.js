import PropTypes from 'prop-types'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import './range.css'

const Range = ({ min, max, ...props }) => {
  const { onUpdate, metrics, disabled } = props
  const [minVal, setMinVal] = useState(metrics.medium)
  const [maxVal, setMaxVal] = useState(metrics.good)
  const minValRef = useRef(min)
  const maxValRef = useRef(max)
  const range = useRef(null)
  // Convert to percentage

  const getPercent = useCallback(value => Math.round(((value - min) / (max - min)) * 100), [min, max])

  // Set width of the range to decrease from the left side
  useEffect(() => {
    const minPercent = getPercent(minVal)
    let maxPercent = 0
    if (maxValRef.current !== maxVal) {
      maxPercent = getPercent(maxVal)
    } else {
      maxPercent = getPercent(maxValRef.current)
    }

    if (range.current) {
      range.current.style.left = `${minPercent}%`
      range.current.style.width = `${maxPercent - minPercent}%`
    }
  }, [minVal, getPercent])

  // Set width of the range to decrease from the right side
  useEffect(() => {
    let minPercent = 0
    const maxPercent = getPercent(maxVal)

    if (minValRef.current !== minVal) {
      minPercent = getPercent(minVal)
    } else {
      minPercent = getPercent(minValRef.current)
    }
    if (range.current) {
      range.current.style.width = `${maxPercent - minPercent}%`
    }
  }, [maxVal, getPercent])

  return (
    <div className="container">
      <input
        type="range"
        disabled={disabled}
        min={min}
        max={max}
        value={minVal}
        onChange={event => {
          const value = Math.min(Number(event.target.value), maxVal - 1)
          setMinVal(value)
          minValRef.current = value
          onUpdate({ min: value, max: maxVal })
        }}
        className="thumb thumb--left"
        style={{ zIndex: minVal > max - 100 && '5' }}
      />
      <input
        type="range"
        disabled={disabled}
        min={min}
        max={max}
        value={maxVal}
        onChange={event => {
          const value = Math.max(Number(event.target.value), minVal + 1)
          setMaxVal(value)
          maxValRef.current = value
          onUpdate({ min: minVal, max: value })
        }}
        className="thumb thumb--right"
      />

      <div className="slider">
        <div className="slider__track" />
        <div ref={range} className="slider__range" />
        <div className="slider__left-value">{minVal}</div>
        <div className="slider__right-value">{maxVal}</div>
      </div>
    </div>
  )
}

Range.propTypes = {
  min: PropTypes.number.isRequired,
  max: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
}

export default Range
