/*========= QI UI ========*/

button,
button.MuiButton-root {
    text-transform: none;
    min-width: auto;
}
button.MuiButtonBase-root.circle-btn {
    height: 32px;
    width: 32px;
    border-radius: 50%;
    box-shadow: 1px 1px 3px rgba(0,0,0,1);
}
button.MuiButton-contained {
    box-shadow: 0 0 0;
}
button a,
button a:hover {
    text-decoration: none !important;
}
.view-hover {
    visibility: hidden;
}
*:hover > .view-hover {
    visibility: visible;
}
img.rounded-circle {
    object-fit: cover;
}
.icon-close {
    cursor: pointer;
}

div {
    .MuiAccordionSummary-root.Mui-expanded,
    .MuiExpansionPanelSummary-root.Mui-expanded {
        min-height: 48px;
        .MuiAccordionSummary-content,
        .MuiExpansionPanelSummary-content {
            margin: 12px 0;
        }
    }
}

/*===== Paper For Select Elements ====*/
/* Avoid overflow on dialogs */
.MuiDialogContent-root  .MuiTextField-root ~ .MuiPaper-root {
    // position: relative;
}
/*========= Chip ========*/
/* Avoid breaking layouts */
.MuiFormControl-root {
    max-width: 100%;
    .MuiChip-label {
        max-width: 100%;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        display: inline-block;
    }
    .MuiChip-root {
        max-width: calc(100% - 10px);
    }
}

/*========= Tab ========*/
.MuiTab-wrapper {
    text-transform: none;
}
.MuiTabs-root {
    .MuiTab-root {
        min-width: 50px;
        flex: 1;
    }
}

/*========= Radio Goup Compact ========*/
.compact-group {
    .MuiRadio-root {
        padding: 0;
        margin: 0 10px;
    }
}

/*========= TextField > force-small ========*/
.MuiTextField-root.force-small{
    input,
    .MuiSelect-select {
        padding: 10px;
    }
    .MuiInputBase-root{
        input,
        .MuiSelect-select {
            padding: 10px;
        }
    }
     .MuiInputLabel-root:not(.MuiInputLabel-shrink) {
        transform: translate(10px, 12px);
        font-size: 14px;
    }
}

/*======= RctSectionLoader ======*/
.RctSectionLoader {
    &.size-small{
        svg {
            transform: scale(0.5);
        }
    }
    &.size-big{
        svg {
            transform: scale(1.5);
        }
    }
}

/*===== CurrencyInputComponent ====*/
.CurrencyInputComponent {
    .CurrencyInput-innerWrapper {
        border-color: #c4c4c4;
        position: absolute;
        top: -6px;
        bottom: -2px;
        right: 0;
        &:not(.CurrencyInput-endAdornmentWrapper){
            left: 0;
            pointer-events: none;
        }
        .CurrencyInput-label {
            color: #999999;
            background: none;
            width: auto;
            padding: 0 3px;
            position: relative;
            margin-left: 8px;
            line-height: 1;
            max-width: calc(100% - 17px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .CurrencyInput-adornment {
            top: 50%;
            opacity: 0.9;
            transform: translate(0, 3px);
            &.CurrencyInput-startAdornment {
                transform: translate(0, -5px);
                left: 10px;
            }
            &.CurrencyInput-endAdornment {
                right: 14px;
            }
        }
        &.focused {
            border-color: #5D92F4;
            border-width: 2px;
            .CurrencyInput-label {
                color: rgb(65, 102, 170);
                margin-left: 7px;
            }
            .CurrencyInput-adornment {
                &.CurrencyInput-startAdornment {
                    left: 9px;
                }
                &.CurrencyInput-endAdornment {
                    right: 13px;
                }
            }
        }
    }
    &:hover {
        .CurrencyInput-innerWrapper {
            border-color: #333;
        }
    }
    &.size-small {
        .CurrencyInput-inputWrapper {
            .CurrencyInput-input {
                padding: 7px 10px 6px !important;
            }
        }
    }
    .CurrencyInput-inputWrapper {
        .CurrencyInput-input {
            color: #333;
            background: none;
        }
    }
}


/*======= SelectPlaceholder ======*/
.SelectPlaceholderComponent-wrapper {
    position: relative;
    max-width: 100%;
    .SelectPlaceholderComponent-label {
        position: absolute;
        // z-index: 100;
        top: -6px;
        font-size: 12px;
        left: 10px;
        line-height: 1;
        background: #fff;
        padding: 0 5px;
        color: #999999;

        background: none;
        width: auto;
        // position: relative;
        // margin-left: 8px;
        max-width: calc(100% - 10px);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .SelectPlaceholderComponent-loader {
        min-height: 56px;
        width: 100%;
        border: 1px solid $input-border-color;
        border-radius: 5px;
        overflow: hidden;
        position: relative;
        svg {
            transform: scale(0.5);
        }
    }
    .MuiPaper-root {
        z-index: 1000;
    }
    &.SelectWithLoader {
        .SelectPlaceholderComponent-loader {
            position: absolute;
            top: 2px;
            left: auto;
            right: 2px;
            background-color: #fff !important;
            z-index: 99;
            min-height: calc(100% - 4px);
            width: 44px;
            border: 0;
        }
        .SelectPlaceholderComponent-children {
            min-height: 56px;
            width: 100%;
            position: relative;
            // z-index: 100;
        }
    }
    &.size-small {
        max-height: 48px;
        .SelectPlaceholderComponent-children {
            min-height: 48px;
            max-height: 48px;
        }
    }
    &.size-tiny {
        max-height: 40px;
        .SelectPlaceholderComponent-children {
            min-height: 40px;
            max-height: 40px;
            .MuiOutlinedInput-root {
                max-height: 40px;
            }
        }
    }
}

/*========= Start Adornment on TextField ========*/
.MuiTextField-root {
    .MuiOutlinedInput-inputAdornedStart {
        .MuiTypography-root {
            left: 40px;
        }
    }
}
/*========= switch Group ========*/
.MuiFormGroup-root.switch-group,
.MuiButtonGroup-root.switch-group {
    border-radius: 20px;
    .MuiButton-root {
        border-radius: 20px;
        box-shadow: 0 0 0;
        line-height: 1;
        min-height: 30px;
        padding: 0 10px;
    }
    .MuiButton-root:not(:first-child) {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }
    .MuiButton-root:not(:last-child) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }
}

/*========= color-picker ========*/
.color-picker-adornment {
    border: 1px solid #ccc;
    border-radius: 4px;
    display: inline-block;
    height: 24px;
    width: 26px;
    cursor: pointer;
}

/*========= avatar ========*/
.MuiAvatar-root.mini-avatar {
    transform: scale(0.85);
    transform-origin: center left;
}
.MuiAvatar-root.micro-avatar {
    transform: scale(0.5);
    transform-origin: center;
}

/*========= qi-list-toolbar ========*/
.qi-list-toolbar .zmdi-assignment-account {
    transform: scaleY(1.1);
}
.qi-list-toolbar .fa-file-excel-o {
    transform: scale(0.9);
}
/*========= badge ========*/
.badge-warning {
    color: $white;
}

/*========= help ========*/
.qiHelpTooltip {
    font-size: 12px;
    box-shadow: 0 0 10px rgba(0,0,0,0.6);
}
.qi-help {
    .badge {
        padding: 0;
        width: 18px;
        height: 18px;
        font-weight: 600;
        text-align: center;
        line-height: 15px;
        padding: 2px;
        font-size: 12px;
        text-indent: 1px;
        transform: scale(0.7);
        transform-origin: top center;
    }
}

/*========= comments ========*/
.deal-comments {
    background: #f9f9f9;
    border-radius: 5px;
    padding: 5px 10px 20px;
    border: 1px solid #ccc;
    position: relative;
    > .label {
        font-size: 12px;
        position: absolute;
        top: -8px;
        background: #fff;
        display: inline-block;
        padding: 2px 8px;
        border-radius: 5px;
    }
    .edit-icon {
        position: absolute;
        left: 0;
        top: -6px;
        transform: scale(0.6);
        transform-origin: top left;
    }
    .MuiTextField-root {
        textarea {
            font-size: 13px;
        }
    }
}

/*========= post-image / post-video ========*/
.post-video,
.post-image {
    position: relative;
    img {
        max-width: 100%;
    }
    .remove-btn {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 1;
        color: #fff;
        box-shadow: 0 0 10px rgba($color: #000, $alpha: 0.05);
        background: rgba($color: #000, $alpha: 0.15);
    }
}

/*========= dialog ========*/
.modal-dialog.xl-modal {
    width: 800px;
    max-width: calc(100vw - 200px);
}
.modal-dialog.xxl-modal {
    width: 1200px;
    max-width: calc(100vw - 200px);
}
.modal-dialog.full-modal {
    width: auto;
    max-width: calc(100vw - 200px);
}

/*========= divider ========*/
.labeled-divider {
    position: relative;
    label.MuiFormControlLabel-root,
    .label {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: #fff;
        padding: 0 10px;
    }
    .toggler {
        cursor: pointer;
        position: absolute;
        top: -10px;
    }
    label.MuiFormControlLabel-root{
        left: auto;
        right: 0;
    }
    &.label-to-left {
        .label {
            left: 0;
            transform: translate(0, -50%);
        }
        .toggler {
            right: 0px;
        }
    }
    &.label-to-right {
        .label {
            right: 0;
            padding-right: 0;
            padding-left: 5px;
        }
        .toggler {
            left: 0px;
        }
        label.MuiFormControlLabel-root{
            left: 0;
            right: auto;
        }
    }
    &.first{
        hr {
            margin-top: 1rem;
        }
        label.MuiFormControlLabel-root,
        .label {
            top: 33%;
            transform: translate(0, -50%);
        }
    }
}

/*========= duallistbox ========*/
.duallistbox-values {
    .MuiListItem-button:hover:after {
        content: "\e082";
        font-family: 'simple-line-icons';
        font-style: normal;
        font-weight: normal;
        font-variant: normal;
        text-transform: none;
        line-height: 1;
        -webkit-font-smoothing: antialiased;
    }
}

/*========= Label ========*/
.MuiInputLabel-outlined {
    z-index: 0;
    &.MuiInputLabel-shrink {
        line-height: 1;
        width: max-content;
        max-width: 125%;
    }
}


/*========= Label ========*/
.projects-icon {
    .btn-icon {
        width: 32px;
        height: 32px;
        min-width: auto;
        border-radius: 100%;
        i {
            margin-left: 0 !important;
            font-size: 24px;
            margin: 0;
        }
    }
}

/*========= XlsxModuleComponent ========*/
.XlsxModuleComponent {
    .dropzone-wrapper,
    .filepicker {
        height: 100%;
        width: 100%;
    }
}
/*========= ImagesModuleComponent / VideosModuleComponent ========*/
.VideosModuleComponent,
.ImagesModuleComponent {
    .rct-block-content {
        display: flex;
        justify-content: center;
        align-items: center;
        .post-video,
        .post-image {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .dropzone-wrapper,
        .filepicker {
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
    &.icon-small{
        .filepicker{
            .filepicker-file-icon {
                padding-left: 35px;
                transform: scale(0.7);
            }
        }
    }
}
/*========= rct-block ========*/
.rct-block {
    display: flex;
    flex-direction: column;
    > .collapse {
        flex: 1;
        > .rct-block-content.bg-gray {
            box-shadow: inset 0 0 5px rgba($color: #000000, $alpha: 0.1);
        }
    }
    .rct-block {
        box-shadow: 0 0 5px 1px rgba(0,0,0,0.05);
    }
}
.rct-block-title{
    &[class*="bg-"] {
        border-radius: 4px 4px 0 0;
    }
    &[class*="bg-"] h4 {
        color: #fff;
    }
    &.rct-block-title-sm {
        padding: 0.3475rem 1.25rem;
    }
}
.rct-block.tabbed-header {
    .rct-block-title {
        padding: 0;
        .MuiTab-root {
            min-width: auto;
            flex: 1;
            min-height: 60px;
        }
    }
}


/*========= pagination ========*/
ul.pagination {
    margin: 0;
}
.page-item {
    .page-link {
        min-width: 44px;
    }
    &.disabled {
        .page-link {
            color: #bfbfbf;
        }
    }
}

/*========= force-outline on TextField ========*/
.MuiTextField-root.force-outline{
    .MuiInputLabel-formControl {
        left: 10px;
    }
    .MuiInput-root {
        border: 1px solid rgba(0,0,0,.23);
        padding: 10px;
        border-radius: 4px;
    }
}


/*========= qi-icon-button ========*/
.qi-icon-button {
    padding: 0;
    cursor: pointer;
    .alert-addon {
        background-color: rgba(0, 0, 0, 0.2);
        display: flex;
        font-size: 1.563rem;
        width: 50px;
        flex-wrap: nowrap;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 0;
        bottom: 0;
        top: 0;
    }
    .upgrade-addon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translate(0, -50%);
    }
    .toggle-btn,
    .remove-btn {
        cursor: pointer;
        position: absolute;
        display: inline-block;
        text-align: center;
        top: 0.3rem;
        right: 0.4rem;
        font-size: 0.7em;
        width: 1.2rem;
        height: 1.2rem;
        line-height: 1.7em;
        border-radius: 50%;
        background: #e4e4e4;
        color: #677080;
        z-index: 2;
    }
    .toggle-btn {
        line-height: 1.9em;
        right: 2rem;
    }
    .btn-label {
        z-index: 1;
        position: absolute;
        top: 0;
        right: 0.4rem;
        left: 4.175rem;
        line-height: 1.9rem;
    }
    small.tip {
        float: right;
        margin-right: 10px;
        line-height: 40px;
        font-size: 10px;
    }
    > div,
    > p {
        margin-bottom: 0;
        padding: 10px;
        display: inline-block;
        padding-left: 4.175rem;
    }
    div {
        line-height: 1;
    }
}

/*========= vertical-separator ========*/
.vertical-separator {
    border-right: 1px solid #efefef;
    height: 100%;
}

/*========= FlexGridList ========*/
.FlexGridListComponent {
    &.FlexGridList-selectable {
        .FlexGridList-item {
            .MuiImageListItem-tile,
            .MuiGridListTile-tile {
                cursor: pointer;
                transition: 0.25s;
                border-radius: 3px;
                &:hover {
                    box-shadow: 0 0 0 3px #5d92f4;
                }
                .MuiImageListItemBar-root,
                .MuiGridListTileBar-root {
                    background-color: rgba(70, 77, 105, 0.75);
                }
            }
            &.selected {
                .MuiImageListItem-tile,
                .MuiGridListTile-tile {
                    &:not(:hover) {
                        box-shadow: 0 0 0 3px rgba(70, 77, 105, 0.95);
                    }
                    .MuiImageListItemBar-root,
                    .MuiGridListTileBar-root {
                        background-color: rgba(70, 77, 105, 0.95);
                    }
                }
            }
        }
    }
}

/*========= Stepper Buttons ========*/
.StepperButtonGroupComponent {
    display: flex;
    justify-content: center;
    align-items: center;
    .steps-wrapper {
        &:before,
        &:after {
            display: table;
            line-height: 0;
            content: "";
        }
        &:after {
            clear: both;
        }
        .step-nav {
            background: #5d92f4;
            color: #fff;
            align-self: stretch;
            display: flex;
            align-items: center;
            padding: .5em;
            border-radius: 4px;
            margin: 0 -5px;
            box-shadow: 0 0 0 1px rgba(255,255,255,1);
            z-index: 0;
            position: relative;
        }
        .prev.step-nav {
            z-index: 10;
            border-radius: 4px 0 0 4px;
        }
        .next.step-nav {
            border-radius: 0 4px 4px 0;
        }
        ul.steps {
            border-radius: 3px;
            list-style: none;
            position: relative;
            left: 0;
            display: block;
            float: left;
            margin: 0;
            overflow: hidden;
            padding: 0;
            border: 4px solid #fff;
            li {
                float: left;
                position: relative;
                &:nth-child(1) {
                    z-index: 9
                }
                &:nth-child(2) {
                    z-index: 8
                }
                &:nth-child(3) {
                    z-index: 7
                }
                &:nth-child(4) {
                    z-index: 6
                }
                &:nth-child(5) {
                    z-index: 5
                }
                &:nth-child(6) {
                    z-index: 4
                }
                &:nth-child(7) {
                    z-index: 3
                }
                &:nth-child(8) {
                    z-index: 2
                }
                &:nth-child(9) {
                    z-index: 1
                }
                &:nth-child(10) {
                    z-index: 0
                }
                > div {
                    font-size: 1rem;
                    line-height: 1.4em;
                    display: block;
                    float: none;
                    padding: 0.3em 0.3em 0.3em 2em;
                    text-decoration: none;
                    margin-right: 0;
                    span.label {
                        font-size: 0.8em;
                        font-weight: 300;
                        text-transform: uppercase;
                    }
                    &:before {
                        content: "";
                        z-index: -1;
                        display: block;
                        position: absolute;
                        top: 0em;
                        width: 2em;
                        height: 2em;
                        border-radius: 6px;
                        -webkit-transform: rotate(45deg);
                        -moz-transform: rotate(45deg);
                        -ms-transform: rotate(45deg);
                        -o-transform: rotate(45deg);
                        transform: rotate(45deg);
                        left: auto;
                        right: -1em;
                        border: 4px solid #ffffff;
                        border-bottom: none;
                        border-left: none
                    }
                }
                > div,
                > div:before {
                    color: #cccccc;
                    background: #f7f7f7;
                }
                &:first-child > div {
                    border-top-left-radius: 3px;
                    border-bottom-left-radius: 3px;
                    padding: 0.3em 0.3em 0.3em 1em
                }
                &:last-child > div {
                    border-top-right-radius: 3px;
                    border-bottom-right-radius: 3px;
                    padding: 0.3em 1em 0.3em 2em
                }
                &.past {
                    cursor: pointer;
                    > div {
                        font-weight: normal;
                        color: #9c9c9c;
                        text-decoration: none;
                        background-color: #f7f7f7;
                        &:before {
                            background-color: #f7f7f7;
                        }
                        a {
                            font-weight: normal
                        }
                    }
                    &:hover{
                        > div {
                            background-color: #efefef;
                            &::before {
                                background-color: #efefef;
                            }
                        }
                    }
                }
                &.active {
                    > div {
                        text-decoration: none;
                        background-color: #5d92f4;
                        color: #ffffff;
                        &:before {
                            background-color: #5d92f4
                        }
                        a {
                            color: #ffffff
                        }
                    }
                }
                &.disabled a {
                    color: #cccccc
                }
            }
        }
        ul.steps,
        ul.steps li > div:before {
            border-color: #ffffff;
        }
    }
    &.light {
        ul.steps {
            li {
                > div:before,
                > div {
                    background: #fff;
                    color: #ccc;
                }
                &.past {
                    > div:before,
                    > div {
                        background: #efefef;
                        color: #9c9c9c;
                    }
                }
            }
        }
        ul.steps,
        ul.steps li > div:before {
            border-color: #f9f9f9;
        }
    }
}

.SelectPlaceholderComponent {
    .MuiInputAdornment-root {
        display: block;
        position: absolute;
        left: 0;
        top: 3px;
        z-index: 1;
    }
    .MuiFilledInput-inputMarginDense {
        min-height: 48px;
        max-height: 48px;
        .MuiChip-label {
            padding-left: 8px;
        }
        .MuiChip-root {
            background: #fff;
            margin: 12px 2px 0;
            height: 22px;
            font-size: 12px;
            .MuiChip-deleteIcon {
                width: 0.7em;
                margin-right: 3px;
            }
        }
    }
}

.FlexInput-PhoneInput-filled .MuiFormLabel-root {
    left: 50px;
}


.block-btn-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin: 0 -10px 10px;
    .block-btn {
        cursor: pointer;
        padding: 15px;
        flex: 1;
        margin: 10px;
        border-radius: 5px;
        background: #fff;
        transition: ease .3s;
        line-height: 1.1;
        border: 2px solid rgba(235,237,242,1);
        box-shadow: 1px 0px 4px rgba(0, 0, 0, 0.05);
        &:hover{
            box-shadow: 1px 2px 4px rgba(0, 0, 0, 0.4);
        }
        &.active{
            box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.4);
            border: 2px solid rgba(70,77,105,0.5);
        }
        figure {
            margin: 0;
            display: flex;
            align-items: center;
            color: #777b8c;
            font-weight: 600;
            i, img {
                margin-right: 10px;
                box-shadow: 0 0 0 3px rgba(0,0,0,0.05);
                padding: 5px;
                border-radius: 50%;
                background: rgba(0,0,0,0.05);
            }
            img {
                width: 50px;
                height: auto;
            }
            i {
                width: 50px;
                height: 50px;
                font-size: 2.2em;
                display: inline-flex;
                align-items: center;
                justify-content: center;
            }
        }
    }
}

.figure-groupbtn-wrapper {
    .MuiButtonGroup-root {
        flex-wrap: wrap;
        .MuiButton-root {
            border: 0;
            padding: 0;
            margin: 0 5px 10px !important;
            &.MuiButton-containedPrimary {
                background-color: transparent;
            }
        }
    }
}

.zmdi-info.zmdi-spam {
    transform: rotate(180deg);
}

.user-photo-uploader {
    position: relative;
    .user-photo-icon {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        color: #fff;
        text-shadow: 1px 0 1px rgba(0,0,0,0.3);
        opacity: 0;
        transition: .1s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    &:hover {
        .user-photo-icon {
            opacity: 1;
            background-color: rgba($color: #000000, $alpha: 0.3);
        }
        .MuiAvatar-root {
            color: transparent;
        }
    }
}

/*========= Counry Input ========*/
.MuiTextField-root {
    .flag-select {
        padding: 0;
    }
    .flag-select__btn {
        color: #4d4d4d;
        border-right: 1px solid rgba(0,0,0,0.2);
        background: transparent;
        display: inline-flex;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        width: 46px;
        margin: 0 6px 0 -3px;
        padding: 3px 8px 3px 3px;
        &:after,
        &[aria-expanded="true"]:after {
            display: none;
        }
        .flag-select__option {
            display: inline-flex;
            align-items: center;
            padding: 0;
            margin: 0;
            height: 100%;
        }
        .flag-select__option__icon {
            display: block;
            margin: 0;
            height: auto;
            width: 100%;
            position: unset;
        }
    }
    .MuiInput-underline {
        .flag-select__btn {
            height: 20px;
            width: 36px;
        }
    }
    .flag-select ~ .MuiOutlinedInput-input {
        .MuiTypography-root {
            left: 70px;
        }
    }
    ul.flag-select__options {
        min-width: 360px;
        margin-left: -15px;
        &::-webkit-scrollbar {
            width: 10px;
        }
        &::-webkit-scrollbar-track {
            background: rgba(0,0,0,0);
            border-radius: 10px;
            transition: all 0.8s ease;
        }
        &::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0);
            min-height: 40px;
            border-radius: 10px;
            transition: all 0.8s ease;
        }
        &:hover::-webkit-scrollbar-track {
            background: rgba(0,0,0,0.05);
        }
        &:hover::-webkit-scrollbar-thumb {
            background: rgba(0,0,0,0.2);
        }
        .filterBox {
            padding: 0 8px 8px;
            min-width: 220px;
            input {
                width: calc(100% + 6px);
                display: block;
                margin: -3px;
                padding: 13px;
                border: 1px solid rgba($color: #000000, $alpha: 0.3);
                border-radius: 4px;
            }
        }
        li {
            > span {
                display: inline-flex;
                min-width: 220px;
                width: auto !important;
                align-items: center;
                line-height: 1;
                height: auto !important;
                max-width: 100vw;
                > img {
                    height: auto;
                    width: 26px;
                    position: unset;
                }
                > span {
                    height: auto;
                    position: unset;
                    font-family: "Heebo", sans-serif;
                    font-weight: 400;
                    color: #464D69;
                    font-size: 14px;
                }
            }
        }
    }
}

.payment-wrap {
    .payment-method-btn {
        cursor: pointer;
        opacity: 0.75;
        // background-color: #f9f9f9;
        min-width: 240px;
        border-radius: 15px;
        transition: .25s;
        transform: scale(0.95);
        box-shadow: 0 0 2px rgba(0, 0, 0, 0.3);
        position: relative;
        &:hover{
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
            transform: scale(1);
            opacity: 1;
        }
        .btn-caption {
            background-color: rgba(0, 0, 0, 0.9);
            position: absolute;
            top: 70%;
            left: 0;
            border-radius: 0 4px 4px 0;
            padding: 10px 30px !important;
            font-size: 15px;
        }
    }
}

.MuiTooltip-popper {
    z-index: 15000;
}

/* React Select com variant="filled" TextField */
.MuiTextField-root {
    .MuiFilledInput-root {
        .ReactSelect-Placeholder {
            opacity: 1;
            font-size: 12px;
            left: 12px;
            bottom: 35%;
        }
    }
}

.attachment-item {
    width: 120px;
    max-width: 120px;
    min-width: 120px;
    text-align: center;
    overflow: hidden;
    div {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        a {
            display: block;
            max-width: 100%;
            white-space: nowrap;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

#preview-wrapper.dropzone-previews {
    display: flex;
    .dz-preview {
        max-width: 120px;
        font-size: 12px;
        svg {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #5d92f4;
        }
    }
    .dz-error-mark,
    .dz-success-mark {
        display: inline-block;
        margin: 5px 5px 5px 0;
    }
}

.conversion-stats-wrapper {
    position: relative;
    padding: 10px;
    width: 70%;
    display: flex;
    align-items: center;
    flex-direction: column;
    text-align: center;
    &:before,
    &:after {
        content: '';
        position: absolute;
        z-index: 3;
        top: 0;
        right: 0;
        bottom: -10%;
        width: 30%;
        background: #fff;
        border-radius: 100% 0 0 0;
        transform: translate(30%,-10%);
        // transform: translate(30%,-3%) rotate(10deg);
    }
    &:before {
        left: 0;
        right: auto;
        border-radius: 0 100% 0 0;
        transform: translate(-30%,-10%);
        // transform: translate(-30%,-3%) rotate(-10deg);
    }
    .conversion-stats {
        padding: 3px 10px;
        color: #fff;
        width: 100%;
        height: 46px;
        position: relative;
        margin-bottom: 8px;
        background-color: $dark;
        > * {
            position: relative;
            z-index: 2;
            line-height: 1;
        }
        > h4 {
            margin: 5px 0 0;
            font-size: 12px;
            font-weight: 400;
        }
        > span {
            font-size: 14px;
        }
        .conversion-stats-aside {
            position: absolute;
            z-index: 5;
            left: 89%;
            color: $dark;
            top: 50%;
            font-size: 13px;
            width: 160px;
            transform: translateY(0%) skew(-15deg, 0deg);
            border: 2px solid;
            border-color: $warning;
            border-left: 0;
            padding: 6px;
            border-radius: 0 3px 3px 0;
            height: 50px;
            max-width: calc(100% - 120px);
            > h5 {
                margin: 0px 0 3px;
            }
            &:before,
            &:after {
                content: '';
                position: absolute;
                height: 10px;
                background: #fff;
                width: 3px;
                right: -2px;
                top: 18px;
            }
            &:after {
                height: 0;
                border: 6px solid;
                border-color: inherit;
                border-bottom: 0;
                border-left-color: transparent;
                border-right-color: transparent;
                right: -6px;
            }
        }
        &:before {
            content: '';
            display: block;
            position: absolute;
            z-index: 1;
            width: 100%;
            height: 100%;
            left: 0;
            top: 0;
            background-color: #464D69;
        }
        &:after {
            content: '';
            display: block;
            position: absolute;
            z-index: 0;
            width: 100%;
            height: 30px;
            top: -10px;
            background: #2a2e3c;
            border-radius: 50% 50% 0 0;
            left: 50%;
            transform: translateX(-50%);
        }
        &:nth-child(1) .conversion-stats-aside {
            margin-left: 5.3%;
        }
        &:nth-child(2) .conversion-stats-aside {
            margin-left: 2.85%;
        }
        &:nth-child(3) .conversion-stats-aside {
            margin-left: 0.7%;
        }
        &:nth-child(4) .conversion-stats-aside {
            margin-left: -1.3%;
        }
        &:nth-child(5) .conversion-stats-aside {
            margin-left: -2.9%;
        }
        &:nth-child(6) .conversion-stats-aside {
            margin-left: -4.2%;
        }
        &:nth-child(7) .conversion-stats-aside {
            margin-left: -4.2%;
        }
        &:first-child:after,
        &:last-child:after {
            background-color: $qiplus-dark;
        }
        &:first-child,
        &:last-child,
        &:first-child:before,
        &:last-child:before {
            background-color: $qiplus-color;
        }
    }
}

.recharts-wrapper {
    .recharts-tooltip-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        background: #fff;
        padding: 10px;
        line-height: 1.1;
        .custom-chart-tooltip-label {
            font-weight: 600;
            line-height: 1.2;
        }
        .custom-chart-tooltip-item {
            font-weight: 500;
        }
    }
}
