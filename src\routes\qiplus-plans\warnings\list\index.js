/**
 * Warnings List Component
 */
import moment from 'moment'
import React, { Component } from 'react'
import { connect } from 'react-redux'
import { Link } from 'react-router-dom'

// Components
import LinearProgress from '@material-ui/core/LinearProgress'
import RctCollapsibleCard from 'Components/RctCollapsibleCard/RctCollapsibleCard'
import { Badge } from 'reactstrap'

// Helpers
import { mockWarnings } from '../model'

class WarningsList extends Component {
  constructor(props) {
    super(props)
    this.state = {
      warnings: [],
      loading: false,
      layout: 'grid',
    }
  }

  componentDidMount() {
    this.loadWarnings()
  }

  loadWarnings = () => {
    this.setState({ loading: true })

    // Simulate API call with mock data
    setTimeout(() => {
      this.setState({
        warnings: mockWarnings,
        loading: false,
      })
    }, 500)
  }

  render() {
    const { warnings, loading, layout } = this.state
    const collectionName = 'warnings'

    return (
      <div className="data-table-wrapper">
        <div className="page-title d-flex justify-content-between align-items-center">
          <div className="page-title-wrap">
            <i className="ti-announcement"></i>
            <h2>
              <span>Avisos</span>
            </h2>
          </div>
          <div className="page-title-right">
            <Link to="/qiplus-plans/warnings/add" className="btn btn-primary btn-sm">
              <i className="ti-plus"></i> Novo Aviso
            </Link>
          </div>
        </div>

        {loading && <LinearProgress />}

        <div className="main-content">
          {warnings.length > 0 ? (
            <div className="row">
              {warnings.map((warning, index) => {
                const detailLink = `/qiplus-plans/warnings/${warning.id}`
                const createdDate = moment(warning.date).format('DD/MM/YYYY HH:mm')
                const modifiedDate = moment(warning.modified).format('DD/MM/YYYY HH:mm')

                return (
                  <RctCollapsibleCard
                    key={index}
                    customClasses=""
                    colClasses="col-sm-12 col-md-6 col-lg-4 w-xs-full"
                    heading={
                      <div className="d-flex justify-content-between align-items-center">
                        <Link to={detailLink}>
                          <span className={!warning.active ? 'text-muted' : ''}>{warning.title}</span>
                        </Link>
                        <Badge color={warning.active ? 'success' : 'secondary'} className="ml-2">
                          {warning.active ? 'Ativo' : 'Inativo'}
                        </Badge>
                      </div>
                    }
                  >
                    <div className="warning-card-content">
                      <div className="mb-15">
                        <h6 className="mb-5">Conteúdo do App:</h6>
                        <p className="text-muted mb-0" style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>
                          {warning.content_app.length > 120
                            ? `${warning.content_app.substring(0, 120)}...`
                            : warning.content_app
                          }
                        </p>
                      </div>

                      <div className="warning-meta">
                        <div className="d-flex justify-content-between align-items-center mb-10">
                          <small className="text-muted">
                            <i className="ti-calendar mr-5"></i>
                            Criado: {createdDate}
                          </small>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mb-10">
                          <small className="text-muted">
                            <i className="ti-pencil mr-5"></i>
                            Modificado: {modifiedDate}
                          </small>
                        </div>

                        <div className="d-flex justify-content-between align-items-center">
                          <small className="text-muted">
                            <i className="ti-user mr-5"></i>
                            Autor: {warning.author}
                          </small>
                        </div>
                      </div>

                      <div className="warning-actions mt-15 pt-15" style={{ borderTop: '1px solid #eee' }}>
                        <div className="d-flex justify-content-between">
                          <Link to={detailLink} className="btn btn-outline-primary btn-sm">
                            <i className="ti-eye mr-5"></i>
                            Visualizar
                          </Link>
                          <Link to={`${detailLink}/edit`} className="btn btn-outline-secondary btn-sm">
                            <i className="ti-pencil mr-5"></i>
                            Editar
                          </Link>
                        </div>
                      </div>
                    </div>
                  </RctCollapsibleCard>
                )
              })}
            </div>
          ) : (
            <RctCollapsibleCard>
              <div className="text-center py-5">
                <i className="ti-announcement" style={{ fontSize: '3rem', color: '#ccc' }}></i>
                <h4 className="mt-3 mb-2">Nenhum aviso encontrado</h4>
                <p className="text-muted mb-4">Ainda não há avisos cadastrados no sistema.</p>
                <Link to="/qiplus-plans/warnings/add" className="btn btn-primary">
                  <i className="ti-plus mr-2"></i>
                  Criar Primeiro Aviso
                </Link>
              </div>
            </RctCollapsibleCard>
          )}
        </div>
      </div>
    )
  }
}

const mapStateToProps = ({ authReducer }) => {
  const { user, account, ownerId } = authReducer;
  const { darkMode } = settings;

  return { user, account, accountId: account.ID, ownerId, darkMode };
}

export default connect(mapStateToProps)(WarningsList)
